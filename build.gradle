plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.3'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.xo'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

wrapper {
    gradleVersion = '8.8'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Core Spring Boot Framework
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    runtimeOnly 'org.postgresql:postgresql'

    // State Machine
    implementation 'org.springframework.statemachine:spring-statemachine-core:4.0.0'
    implementation 'org.springframework.statemachine:spring-statemachine-recipes-common:4.0.0'

    // Database Migration
    implementation 'org.liquibase:liquibase-core'

    // JWT Authentication
    implementation 'io.jsonwebtoken:jjwt-api:0.12.6'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.6'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.6'

    // API Documentation
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.6'

    // Email Services
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'com.sun.mail:jakarta.mail:2.0.1'

    // PDF Generation & Processing
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'com.itextpdf:itext-core:9.2.0'
    implementation 'com.itextpdf:html2pdf:6.2.0'

    // QR Code Generation
    implementation 'com.google.zxing:core:3.5.3'
    implementation 'com.google.zxing:javase:3.5.3'

    // Excel/Office Documents
    implementation 'org.apache.poi:poi:5.4.1'
    implementation 'org.apache.poi:poi-ooxml:5.4.1'

    // HTTP Client
    implementation 'org.apache.httpcomponents.client5:httpclient5:5.4.2'

    // Utilities
    implementation 'com.google.guava:guava:33.4.0-jre'

    // Payment Processing
    implementation 'com.stripe:stripe-java:27.0.0'

    // AWS Services
    implementation 'software.amazon.awssdk:s3:2.31.56'
    implementation 'software.amazon.awssdk:ses:2.31.56'

    // Firebase/Google Services
    implementation 'com.google.firebase:firebase-admin:9.5.0'

    // Logging
    implementation 'com.agido:logback-elasticsearch-appender:3.0.16'

    // Code Generation (Compile-time)
    compileOnly 'org.projectlombok:lombok'
    implementation 'org.mapstruct:mapstruct:1.6.3'
    implementation 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

    // Annotation Processors
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.6.3'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    testAnnotationProcessor 'org.mapstruct:mapstruct-processor:1.6.3'
}

tasks.named('test') {
    useJUnitPlatform()
}