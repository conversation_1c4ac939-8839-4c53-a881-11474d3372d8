<configuration>
    <!-- Log to console in local profile -->
    <springProfile name="local">
        <include resource="org/springframework/boot/logging/logback/base.xml"/>
    </springProfile>

    <springProfile name="!local">
    <statusListener class="ch.qos.logback.core.status.OnConsoleStatusListener"/>
    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <!-- Properties from Spring environment -->
    <springProperty scope="context" name="elasticHost" source="elasticsearch.logging.host"/>
    <springProperty scope="context" name="elasticIndex" source="elasticsearch.logging.index"/>
    <springProperty scope="context" name="elasticUser" source="elasticsearch.logging.username"/>
    <springProperty scope="context" name="elasticPass" source="elasticsearch.logging.password"/>

    <appender name="ELASTIC" class="com.agido.logback.elasticsearch.ElasticsearchAppender">
        <url>https://${elasticUser}:${elasticPass}@${elasticHost}/_bulk</url>
        <index>${elasticIndex}</index>
        <maxRetries>5</maxRetries>
        <includeMdc>true</includeMdc>
        <objectSerialization>true</objectSerialization>

        <authentication class="com.agido.logback.elasticsearch.config.BasicAuthentication" />
        <properties>
            <esProperty>
                <name>logLevel</name>
                <value>%level</value>
            </esProperty>
            <esProperty>
                <name>thread</name>
                <value>%thread</value>
            </esProperty>
            <esProperty>
                <name>stacktrace</name>
                <value>%ex</value>
            </esProperty>
            <esProperty>
                <name>logger</name>
                <value>%logger</value>
            </esProperty>
        </properties>

        <headers>
            <header>
                <name>Content-Type</name>
                <value>application/x-ndjson</value>
            </header>
        </headers>

    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ELASTIC"/>
    </root>
    </springProfile>
</configuration>
