databaseChangeLog:
  - changeSet:
      id: 17
      author: <PERSON><PERSON><PERSON>_Ka<PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: entry_pass_schedule
            schemaName: xo
      changes:
        - dropTable:
            tableName: entry_pass_schedule
            schemaName: xo
  - changeSet:
      id: 18
      author: <PERSON><PERSON><PERSON>_Ka<PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: booking_tier_schedule
            schemaName: xo
      changes:
        - dropTable:
            tableName: booking_tier_schedule
            schemaName: xo