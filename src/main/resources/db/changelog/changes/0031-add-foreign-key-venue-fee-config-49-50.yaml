databaseChangeLog:
  - changeSet:
      id: 49
      author: Men<PERSON><PERSON>_Kaskiris
      preConditions:
        - onFail: MARK_RAN
        - and:
          - tableExists:
              tableName: market_place_venue_config
              schemaName: xo
          - not:
              uniqueConstraintExists:
                tableName: market_place_venue_config
                columnNames: venue_id
                schemaName: xo
      changes:
        - addUniqueConstraint:
            schemaName: xo
            tableName: market_place_venue_config
            columnNames: venue_id
            constraintName: uk_mkt_venue_config_venue_id
  - changeSet:
      id: 50
      author: Menelaos_Kaskiris
      preConditions:
        - onFail: MARK_RAN
        - and:
          - tableExists:
              tableName: mkt_venue_fee_config
              schemaName: xo
          - tableExists:
              tableName: market_place_venue_config
              schemaName: xo
          - uniqueConstraintExists:
              tableName: market_place_venue_config
              columnNames: venue_id
              schemaName: xo
          - not:
              foreignKeyConstraintExists:
                foreignKeyName: fk_mkt_venue_fee_config_venue_id
                baseTableName: mkt_venue_fee_config
                baseTableSchemaName: xo
      changes:
        - addForeignKeyConstraint:
            baseTableName: mkt_venue_fee_config
            baseTableSchemaName: xo
            baseColumnNames: venue_id
            constraintName: fk_mkt_venue_fee_config_venue_id
            referencedTableName: market_place_venue_config
            referencedTableSchemaName: xo
            referencedColumnNames: venue_id
            onDelete: CASCADE
            onUpdate: CASCADE
