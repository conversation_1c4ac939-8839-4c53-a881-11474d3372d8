databaseChangeLog:
  # create event table
  - changeSet:
      id: 4
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: event
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: event
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: venue_id
                  type: text
              - column:
                  name: status
                  type: text
              - column:
                  name: display_price
                  type: float
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
              - column:
                  name: booking_deadline
                  type: timestamp
              - column:
                  name: start_at
                  type: timestamp
              - column:
                  name: end_at
                  type: timestamp
              - column:
                  name: created_by
                  type: int
  # create event_attribute table
  - changeSet:
      id: 5
      author: <PERSON><PERSON><PERSON>_Ka<PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: event_attribute
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: event_attribute
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: attribute_id
                  type: int
              - column:
                  name: event_id
                  type: int
              - column:
                  name: language_id
                  type: int
              - column:
                  name: attribute_value
                  type: text
        - addForeignKeyConstraint:
            baseColumnNames: event_id
            baseTableName: event_attribute
            baseTableSchemaName: xo
            constraintName: fk_event_id_event_atr
            referencedColumnNames: id
            referencedTableName: event
            referencedTableSchemaName: xo
        - addForeignKeyConstraint:
            baseColumnNames: attribute_id
            baseTableName: event_attribute
            baseTableSchemaName: xo
            constraintName: fk_atr_id_event_atr
            referencedColumnNames: id
            referencedTableName: attribute
            referencedTableSchemaName: xo
        - addForeignKeyConstraint:
            baseColumnNames: language_id
            baseTableName: event_attribute
            baseTableSchemaName: xo
            constraintName: fk_lang_id_event_atr
            referencedColumnNames: id
            referencedTableName: language
            referencedTableSchemaName: xo
