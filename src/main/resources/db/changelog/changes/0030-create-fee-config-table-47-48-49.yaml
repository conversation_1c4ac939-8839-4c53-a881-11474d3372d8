databaseChangeLog:
  - changeSet:
      id: 47
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: marketplace_venue_fee_config
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: marketplace_venue_fee_config
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: venue_id
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: app_fee_perc
                  type: numeric(5,4)
                  constraints:
                    nullable: false
              - column:
                  name: app_fee_fixed_per_transaction
                  type: numeric(19,2)
                  constraints:
                    nullable: false
              - column:
                  name: app_fee_fixed_per_entry_pass
                  type: numeric(19,2)
                  constraints:
                    nullable: false
              - column:
                  name: app_fee_min
                  type: numeric(19,2)
              - column:
                  name: app_fee_max
                  type: numeric(19,2)
              - column:
                  name: app_fee_to_customer_perc
                  type: numeric(5,4)
                  constraints:
                    nullable: false
              - column:
                  name: psp_fee_perc
                  type: numeric(5,4)
                  constraints:
                    nullable: false
              - column:
                  name: psp_fee_fixed
                  type: numeric(19,2)
                  constraints:
                    nullable: false
              - column:
                  name: pass_psp_fees
                  type: boolean
                  constraints:
                    nullable: false
                  defaultValueBoolean: false
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
        - addUniqueConstraint:
            schemaName: xo
            tableName: marketplace_venue_fee_config
            columnNames: venue_id
            constraintName: uk_fee_config_venue_id
  - changeSet:
      id: 48
      author: Menelaos_Kaskiris
      preConditions:
        - tableExists:
              tableName: booking
              schemaName: xo
        - not:
            columnExists:
              tableName: booking
              columnName: application_fee
              schemaName: xo
        - not:
            columnExists:
              tableName: booking
              columnName: psp_fee
              schemaName: xo
      changes:
        - addColumn:
            tableName: booking
            schemaName: xo
            columns:
              - column:
                  name: application_fee
                  type: numeric(19,2)
              - column:
                  name: psp_fee
                  type: numeric(19,2)

