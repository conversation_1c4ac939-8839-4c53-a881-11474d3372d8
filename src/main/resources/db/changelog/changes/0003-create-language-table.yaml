databaseChangeLog:
  - changeSet:
      id: 3
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: language
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: language
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: name #create index on this
                  type: text
              - column:
                  name: iso_code
                  type: varchar(3)
              - column:
                  name: locale
                  type: text