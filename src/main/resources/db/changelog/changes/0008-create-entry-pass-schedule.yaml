databaseChangeLog:
  - changeSet:
      id: 14
      author: Keyur_kalariya
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: entry_pass
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: entry_pass
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: event_id
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: booking_id
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: booking_tier_id
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: reference_number
                  type: text
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: owner_name
                  type: text
                  constraints:
                    nullable: true
              - column:
                  name: owner_id
                  type: int
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            baseColumnNames: booking_id
            baseTableName: entry_pass
            baseTableSchemaName: xo
            constraintName: fk_booking_id_entry_pass
            referencedColumnNames: id
            referencedTableName: booking
            referencedTableSchemaName: xo
        - addForeignKeyConstraint:
            baseColumnNames: event_id
            baseTableName: entry_pass
            baseTableSchemaName: xo
            constraintName: fk_event_id_entry_pass
            referencedColumnNames: id
            referencedTableName: event
            referencedTableSchemaName: xo
        - addForeignKeyConstraint:
            baseColumnNames: booking_tier_id
            baseTableName: entry_pass
            baseTableSchemaName: xo
            constraintName: fk_booking_tier_id_entry_pass
            referencedColumnNames: id
            referencedTableName: booking_tier
            referencedTableSchemaName: xo

  - changeSet:
      id: 15
      author: Keyur_kalariya
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: entry_pass_schedule
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: entry_pass_schedule
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: entry_pass_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: valid_from
                  type: timestamp
                  constraints:
                    nullable: true
              - column:
                  name: valid_until
                  type: timestamp
                  constraints:
                    nullable: true
              - column:
                  name: used
                  type: boolean
                  constraints:
                    nullable: false
        - addForeignKeyConstraint:
            baseColumnNames: entry_pass_id
            baseTableName: entry_pass_schedule
            baseTableSchemaName: xo
            constraintName: fk_entry_pass_id_entry_pass_schedule
            referencedColumnNames: id
            referencedTableName: entry_pass
            referencedTableSchemaName: xo