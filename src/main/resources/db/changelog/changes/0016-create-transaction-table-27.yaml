databaseChangeLog:
  - changeSet:
      id: 27
      author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: payment_transaction
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: payment_transaction
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: booking_id
                  type: int
              - column:
                  name: user_id
                  type: int
              - column:
                  name: payment_provider
                  type: text
              - column:
                  name: currency
                  type: varchar(3)
              - column:
                  name: provider_session_id
                  type: text
              - column:
                  name: provider_transaction_id
                  type: text
              - column:
                  name: platform_fee
                  type: numeric(19,2)
              - column:
                  name: total_amount
                  type: numeric(19,2)
              - column:
                  name: transaction_status
                  type: text
              - column:
                  name: failure_reason
                  type: text
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
              - column:
                  name: completed_at
                  type: timestamp
        - addForeignKeyConstraint:
            baseColumnNames: booking_id
            baseTableName: payment_transaction
            baseTableSchemaName: xo
            constraintName: fk_booking_id_payment_transaction
            referencedColumnNames: id
            referencedTableName: booking
            referencedTableSchemaName: xo