databaseChangeLog:
  - changeSet:
      id: 41
      author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      preConditions:
        - not:
            tableExists:
              tableName: device_token
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: device_token
            columns:
              - column:
                  name: user_id
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: device_token
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: token_expiry
                  type: timestamp
              - column:
                  name: device_name
                  type: text
              - column:
                  name: device_os
                  type: text
              - column:
                  name: last_notification_sent
                  type: timestamp
              - column:
                  name: push_enabled
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: is_active
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
        - addPrimaryKey:
            tableName: device_token
            schemaName: xo
            columnNames: user_id, device_token
