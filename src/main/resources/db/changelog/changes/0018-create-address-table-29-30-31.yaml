databaseChangeLog:
  - changeSet:
      id: 29
      author: Keyur_kalariya
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: address
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: address
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: address_line1
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: address_line2
                  type: text
              - column:
                  name: city
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: state
                  type: text
              - column:
                  name: country
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: zip_code
                  type: text
              - column:
                  name: latitude
                  type: double precision
              - column:
                  name: longitude
                  type: double precision
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
              - column:
                  name: created_by
                  type: int
              - column:
                  name: updated_by
                  type: int

  - changeSet:
      id: 30
      author: Keyur_kalariya
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: event
            schemaName: xo
        - not:
            columnExists:
              tableName: event
              schemaName: xo
              columnName: custom_address_id
      changes:
        - addColumn:
            schemaName: xo
            tableName: event
            columns:
              - column:
                  name: custom_address_id
                  type: int
        - addForeignKeyConstraint:
            baseColumnNames: custom_address_id
            baseTableName: event
            baseTableSchemaName: xo
            constraintName: fk_custom_address_id_event
            referencedColumnNames: id
            referencedTableName: address
            referencedTableSchemaName: xo
  - changeSet:
      id: 31
      author: Keyur_kalariya
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: event
            schemaName: xo
        - not:
            columnExists:
              tableName: event
              schemaName: xo
              columnName: use_custom_address
      changes:
        - addColumn:
            schemaName: xo
            tableName: event
            columns:
              - column:
                  name: use_custom_address
                  type: boolean
