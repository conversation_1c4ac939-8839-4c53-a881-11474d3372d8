databaseChangeLog:
  - changeSet:
      id: 44
      author: <PERSON><PERSON><PERSON><PERSON><PERSON>
      changes:
        - addColumn:
            schemaName: xo
            tableName: event
            columns:
              - column:
                  name: allow_pre_sale_discounted_prices
                  type: BOOLEAN

        - addColumn:
            schemaName: xo
            tableName: booking_tier
            columns:
              - column:
                  name: pre_sale_discounted_price
                  type: numeric(19, 2)

        - addColumn:
            schemaName: xo
            tableName: booking_item
            columns:
              - column:
                  name: purchase_price
                  type: numeric(19, 2)
              - column:
                  name: total_price
                  type: numeric(19, 2)

        - addColumn:
            schemaName: xo
            tableName: booking
            columns:
              - column:
                  name: has_pre_sale_discounts
                  type: BOOLEAN

  - changeSet:
      id: 45
      author: <PERSON><PERSON><PERSON><PERSON><PERSON>
      changes:
        - update:
            schemaName: xo
            tableName: event
            columns:
              - column:
                  name: allow_pre_sale_discounted_prices
                  valueBoolean: "false"

        - update:
            schemaName: xo
            tableName: booking
            columns:
              - column:
                  name: has_pre_sale_discounts
                  valueBoolean: "false"

  - changeSet:
      id: 46
      author: <PERSON><PERSON><PERSON><PERSON><PERSON>
      changes:
        - sql:
            sql: |
              UPDATE xo.booking_item bi
              SET purchase_price = bt.pay_online_price
              FROM xo.booking_tier bt
              WHERE bi.booking_tier_id = bt.id;

        - sql:
            sql: |
              UPDATE xo.booking_item
              SET total_price = purchase_price * quantity;
