databaseChangeLog:
  - changeSet:
      id: 25
      author: <PERSON><PERSON><PERSON>_Kaskiris
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: booking
            schemaName: xo
        - and:
            - not:
                columnExists:
                  tableName: booking
                  columnName: total_amount
                  schemaName: xo
      changes:
        - addColumn:
            tableName: booking
            schemaName: xo
            columns:
              - column:
                  name: total_amount
                  type: numeric(19,2)
  - changeSet:
      id: 26
      author: <PERSON>elaos_Kaskiris
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: booking
            schemaName: xo
        - and:
            columnExists:
              tableName: booking
              columnName: payment_amount
              schemaName: xo
        - and:
            - not:
                columnExists:
                  tableName: booking
                  columnName: net_amount
                  schemaName: xo
      changes:
        - renameColumn:
            tableName: booking
            schemaName: xo
            oldColumnName: payment_amount
            newColumnName: net_amount
            columnDataType: numeric(19,2)
