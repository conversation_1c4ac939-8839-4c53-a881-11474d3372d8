databaseChangeLog:
  - changeSet:
      id: 19
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: entry_pass
            schemaName: xo
        - and:
            - not:
                columnExists:
                  tableName: entry_pass
                  columnName: used
                  schemaName: xo
      changes:
        - addColumn:
            tableName: entry_pass
            schemaName: xo
            columns:
              - column:
                  name: used
                  type: text
  - changeSet:
      id: 20
      author: <PERSON><PERSON>os_Kaskiris
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: entry_pass
            schemaName: xo
        - and:
            - not:
                columnExists:
                  tableName: entry_pass
                  columnName: used_at
                  schemaName: xo
      changes:
        - addColumn:
            tableName: entry_pass
            schemaName: xo
            columns:
              - column:
                  name: used_at
                  type: timestamp
  - changeSet:
      id: 21
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: entry_pass
            schemaName: xo
        - and:
            - not:
                columnExists:
                  tableName: entry_pass
                  columnName: price
                  schemaName: xo
      changes:
        - addColumn:
            tableName: entry_pass
            schemaName: xo
            columns:
              - column:
                  name: price
                  type: numeric(19,2)