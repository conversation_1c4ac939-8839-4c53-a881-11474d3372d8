databaseChangeLog:
  - changeSet:
      id: 32
      author: moham<PERSON>_karmi
      changes:
        - createTable:
            schemaName: xo
            tableName: market_place_event_config
            columns:
              - column:
                  name: id
                  type: INT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: visible
                  type: BOOLEAN
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: INT
              - column:
                  name: updated_by
                  type: INT
              - column:
                  name: created_at
                  type: TIMESTAMP
              - column:
                  name: updated_at
                  type: TIMESTAMP
              - column:
                  name: event_id
                  type: INT
        - createTable:
            schemaName: xo
            tableName: market_place_venue_config
            columns:
              - column:
                  name: id
                  type: INT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: visible
                  type: BOOLEAN
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: INT
              - column:
                  name: updated_by
                  type: INT
              - column:
                  name: created_at
                  type: TIMESTAMP
              - column:
                  name: updated_at
                  type: TIMESTAMP
              - column:
                  name: venue_id
                  type: VARCHAR(255)