databaseChangeLog:
  - changeSet:
      id: 38
      author: <PERSON><PERSON><PERSON>
      preConditions:
        - not:
            columnExists:
              tableName: booking
              columnName: require_confirmation
              schemaName: xo
      changes:
        - addColumn:
            tableName: booking
            schemaName: xo
            columns:
              - column:
                  name: require_confirmation
                  type: text
  - changeSet:
      id: 39
      author: <PERSON><PERSON><PERSON>
      changes:
        - update:
            tableName: booking
            schemaName: xo
            columns:
              - column:
                  name: require_confirmation
                  valueComputed: |
                    CASE
                      WHEN EXISTS (
                        SELECT 1
                        FROM xo.booking_item bi
                        JOIN xo.booking_tier bt ON bi.booking_tier_id = bt.id
                        WHERE bi.booking_id = booking.id
                          AND bt.require_confirmation = 'YES'
                      )
                      THEN 'YES'
                      ELSE 'NO'
                    END