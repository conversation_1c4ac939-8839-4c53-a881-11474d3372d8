databaseChangeLog:
  - changeSet:
      id: 28
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: webhook_log
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: webhook_log
            columns:
              - column:
                  name: id
                  type: text
                  constraints:
                    primaryKey: true
              - column:
                  name: provider
                  type: text
              - column:
                  name: event_type
                  type: text
              - column:
                  name: received_at
                  type: timestamp