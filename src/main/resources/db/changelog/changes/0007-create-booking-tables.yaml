databaseChangeLog:
  # create booking table
  - changeSet:
      id: 12
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: booking
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: booking
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: event_id
                  type: int
              - column:
                  name: owner_id
                  type: int
              - column:
                  name: status
                  type: text
              - column:
                  name: booking_number
                  type: text
              - column:
                  name: booking_name
                  type: text
              - column:
                  name: owner_name
                  type: text
              - column:
                  name: owner_email
                  type: text
              - column:
                  name: owner_phone
                  type: text
              - column:
                  name: created_by
                  type: int
              - column:
                  name: updated_by
                  type: int
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
              - column:
                  name: arrival_time
                  type: timestamp
              - column:
                  name: comment
                  type: text
              - column:
                  name: booking_occasion
                  type: text
        - addForeignKeyConstraint:
            baseColumnNames: event_id
            baseTableName: booking
            baseTableSchemaName: xo
            constraintName: fk_event_id_booking
            referencedColumnNames: id
            referencedTableName: event
            referencedTableSchemaName: xo
  # create booking_item table
  - changeSet:
      id: 13
      author: Menelaos_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: booking_item
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: booking_item
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: booking_id
                  type: int
              - column:
                  name: booking_tier_id
                  type: int
              - column:
                  name: quantity
                  type: int
              - column:
                  name: num_of_people
                  type: int
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
        - addForeignKeyConstraint:
            baseColumnNames: booking_id
            baseTableName: booking_item
            baseTableSchemaName: xo
            constraintName: fk_booking_id
            referencedColumnNames: id
            referencedTableName: booking
            referencedTableSchemaName: xo
        - addForeignKeyConstraint:
            baseColumnNames: booking_tier_id
            baseTableName: booking_item
            baseTableSchemaName: xo
            constraintName: fk_booking_tier_id
            referencedColumnNames: id
            referencedTableName: booking_tier
            referencedTableSchemaName: xo

