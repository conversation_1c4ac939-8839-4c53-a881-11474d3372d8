databaseChangeLog:
  # create booking_tier table
  - changeSet:
      id: 9
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: booking_tier
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: booking_tier
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: type
                  type: text
              - column:
                  name: pay_online_price
                  type: float
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
              - column:
                  name: event_id
                  type: int
              - column:
                  name: available_qty
                  type: int
              - column:
                  name: min_persons
                  type: int
              - column:
                  name: max_persons
                  type: int
              - column:
                  name: require_confirmation
                  type: text
              - column:
                  name: created_by
                  type: int
        - addForeignKeyConstraint:
            baseColumnNames: event_id
            baseTableName: booking_tier
            baseTableSchemaName: xo
            constraintName: fk_booking_tier_event_id
            referencedColumnNames: id
            referencedTableName: event
            referencedTableSchemaName: xo
  # create booking_tier_attribute table
  - changeSet:
      id: 10
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: booking_tier_attribute
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: booking_tier_attribute
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: booking_tier_id
                  type: int
              - column:
                  name: attribute_id
                  type: int
              - column:
                  name: language_id
                  type: int
              - column:
                  name: attribute_value
                  type: text
        - addForeignKeyConstraint:
            baseColumnNames: booking_tier_id
            baseTableName: booking_tier_attribute
            baseTableSchemaName: xo
            constraintName: fk_booking_tier_id_booking_tier_atr
            referencedColumnNames: id
            referencedTableName: booking_tier
            referencedTableSchemaName: xo
        - addForeignKeyConstraint:
            baseColumnNames: attribute_id
            baseTableName: booking_tier_attribute
            baseTableSchemaName: xo
            constraintName: fk_atr_id_booking_tier_atr
            referencedColumnNames: id
            referencedTableName: attribute
            referencedTableSchemaName: xo
        - addForeignKeyConstraint:
            baseColumnNames: language_id
            baseTableName: booking_tier_attribute
            baseTableSchemaName: xo
            constraintName: fk_lang_id_booking_tier_atr
            referencedColumnNames: id
            referencedTableName: language
            referencedTableSchemaName: xo
  # create booking_tier_schedule table
  - changeSet:
      id: 11
      author: Menelaos_Kaskiris
      preConditions:
        - onFail: MARK_RAN
        - not:
            tableExists:
              tableName: booking_tier_schedule
              schemaName: xo
      changes:
        - createTable:
            schemaName: xo
            tableName: booking_tier_schedule
            columns:
              - column:
                  name: id
                  type: serial
                  constraints:
                    primaryKey: true
              - column:
                  name: booking_tier_id
                  type: int
              - column:
                  name: valid_from
                  type: timestamp
              - column:
                  name: valid_until
                  type: timestamp
        - addForeignKeyConstraint:
            baseColumnNames: booking_tier_id
            baseTableName: booking_tier_schedule
            baseTableSchemaName: xo
            constraintName: fk_booking_tier_id_booking_tier_sch
            referencedColumnNames: id
            referencedTableName: booking_tier
            referencedTableSchemaName: xo
