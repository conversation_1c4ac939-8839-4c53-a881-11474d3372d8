# Initialize values for attribute table
databaseChangeLog:
  - changeSet:
      id: 7
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: attribute
            schemaName: xo
      changes:
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: image
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: banner
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: reel
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: booking-deadline
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: title
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: description
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: short-description
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: number-of-drinks
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: bottle-credit
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: minimum-spent
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: tier-name
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: included-consumption-amount
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: included-consumption-description
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: lineup
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: minimum-age
        - insert:
            schemaName: xo
            tableName: attribute
            columns:
              - column:
                  name: name
                  value: music-style
  # Initialize None & English language
  - changeSet:
      id: 8
      author: Menelaos_Kaskiris
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: language
            schemaName: xo
      changes:
        - insert:
            schemaName: xo
            tableName: language
            columns:
              - column:
                  name: name
                  value: 'none'
        - insert:
            schemaName: xo
            tableName: language
            columns:
              - column:
                  name: name
                  value: 'english'
              - column:
                  name: iso_code
                  value: 'en'
              - column:
                  name: locale
                  value: 'en_GB'
