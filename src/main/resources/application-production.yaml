server:
  port: ${PORT:8080}

spring:
  mail:
    host: smtp.gmail.com            # SMTP server
    port: 587                       # Port (for TLS/STARTTLS)
    username: <EMAIL>   # Your email
    password: your-email-password    # Your email password
    properties:
      mail:
        smtp:
          auth: true                # Enable authentication
          starttls:
            enable: true            # Enable STARTTLS

  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master-local.yaml
    database-change-log-table: DATABASECHANGELOG
    database-change-log-lock-table: DATABASECHANGELOGLOCK

  cloud:
    contabo:
      object-store:
        endpoint: https://eu2.contabostorage.com
        bucket-prefix: 6b90789d810d4d6b8273e135486f6680
        region: eu-central-1
        bucket: xo-dev-media
        access-key: ${CONTABO_ACCESS_KEY}
        secret-key: ${CONTABO_SECRET_KEY}
    ovh:
      object-store:
        endpoint: https://s3.de.io.cloud.ovh.net
        region: de
        bucket: xo-media-prod
        access-key: ${OVH_ACCESS_KEY}
        secret-key: ${OVH_SECRET_KEY}
    s3mock:
      object-store:
        endpoint: http://*************:9090
        region: eu-central-1
        bucket: mybucket
        access-key: test
        secret-key: test
    aws:
      region:
        static: eu-north-1
      ses:
        region: eu-north-1
        access-key: ${AWS_SES_USER}
        secret-key: ${AWS_SES_PASSWORD}

app:
  clients:
    go:
      basePath: https://admin.thexo.app
      api-key: ${GO_API_KEY}
    payment:
      stripe:
        secret-key: ${STRIPE_SECRET_KEY}
        connect-endpoint-secret: ${STRIPE_CONNECT_ENDPOINT_SECRET}
        enabled: true
    email:
      provider: ses # Possible values: java-mail, ses
    media:
      provider: ovh
email:
  support:
    recipient: <EMAIL>
  transactional:
    sender: <EMAIL>

elasticsearch:
  logging:
    index: logs-xo-production-%d{yyyy-MM-dd}