server:
  port: ${PORT:8080}

spring:
  application:
    name: backend
  datasource:
    url: ${DATASOURCE_URL}
    username: ${DATASOURCE_USERNAME}
    password: ${DATASOURCE_PASSWORD}
    driver-class-name: org.postgresql.Driver

  liquibase:
    enabled: false
  web:
    resources:
      add-mappings: false
  jpa:
    open-in-view: false
    database: postgresql
    properties:
      hibernate:
        jdbc:
          time_zone: UTC
        format_sql: false
        default_schema: xo
  servlet:
    multipart:
      max-file-size: 250MB
      max-request-size: 250MB

  mail:
    host: ${EMAIL_HOSTNAME}            # SMTP server
    port: ${EMAIL_PORT}                       # Port (for TLS/STARTTLS)
    username: ${EMAIL_USERNAME}   # Your email
    password: ${EMAIL_PASSWORD}    # Your email password
    properties:
      mail:
        smtp:
          auth: true                # Enable authentication
          starttls:
            enable: true            # Enable STARTTLS


  thymeleaf:
    prefix: classpath:/template/
    suffix: .html
    cache: false
    encoding: UTF-8

management:
  endpoints:
    web:
      exposure:
        include: info, health, env
      base-path: /actuator
  endpoint:
    health:
      show-details: always
  info:
    git:
      mode: full
  health:
    mail:
      enabled: false

httpConnectionPool:
  maxTotal: 20
  defaultMaxPerRoute: 10

cors-config:
  capability:
    events:
      url-pattern: '/v1/events/**'
      method: GET,POST,PUT,PATCH,OPTIONS,DELETE
      origins: '*'
    bookings:
      url-pattern: '/v1/bookings/**'
      method: GET,POST,PUT,PATCH,OPTIONS,DELETE
      origins: '*'
    media:
      url-pattern: '/v1/media/**'
      method: GET,POST,PUT,OPTIONS,DELETE
      origins: '*'
    booking-tiers:
      url-pattern: '/v1/booking-tiers/**'
      method: GET,POST,PUT,OPTIONS,DELETE
      origins: '*'
    venues:
      url-pattern: '/v1/venues/**'
      method: GET,POST,PUT,PATCH,OPTIONS,DELETE
      origins: '*'
    marketplace:
      url-pattern: '/v1/marketplace/**'
      method: GET,POST,PUT,PATCH,OPTIONS,DELETE
      origins: '*'
    enums:
      url-pattern: '/v1/enums/**'
      method: GET,OPTIONS
      origins: '*'
    device-tokens:
      url-pattern: '/v1/device-tokens/**'
      method: GET,POST,PATCH,DELETE,OPTIONS
      origins: '*'
    emails:
      url-pattern: '/v1/emails/**'
      method: POST,OPTIONS
      origins: '*'

bookings:
  max-ticket-per-user-per-event: 5
  max-reservation-per-user-per-event: 2
  mixed-booking-allowed: false

marketplace:
  fees:
    default:
      app-fee-perc: 0.00
      app-fee-fixed-per-transaction: 0.00
      app-fee-fixed-per-entry-pass: 1.50
      app-fee-min: 1.50
      app-fee-max: 25.00
      app-fee-to-customer-perc: 1.00
      psp-fee-perc: 0.00
      psp-fee-fixed: 0.00
      pass-psp-fees: false

app:
  clients:
    go:
      timeout: 10000
      basePath: ${GO_BASE_PATH}
      api-key: ${GO_API_KEY}
      endpoints:
        current-user: /api/v1/auth/currentuser
        venue-details: /api/v1/stores/
        venue-overview: /api/v1/stores/overview
        user-profile: /api/v1/auth/profile
    venue-info:
      provider: go
    user-info:
      provider: go
    email:
      provider: ses # Possible values: java-mail, ses
    media:
      provider: ovh
    payment:
      stripe:
        secret-key: ${STRIPE_SECRET_KEY}
        connect-endpoint-secret: ${STRIPE_CONNECT_ENDPOINT_SECRET}
        enabled: true
        success-url: https://thexo.app/application/bookings/{bookingId}/payment/success
        success-payment-link-url: https://www.thexo.app/thank-you-email
        cancel-url: https://thexo.app/application/bookings/{bookingId}/payment/failure
        cancel-payment-link-url: https://www.thexo.app/cancel-payment
        checkout-expiration-minutes: 30
        pay-by-link-expiration-hours: 24
    notifications:
      provider: fcm
      firebase:
        credentials-b64: ${FIREBASE_CONFIG}

email:
  support:
    sender: <EMAIL>
    sender-name: XO Support Team
    recipient: <EMAIL>
  transactional:
    sender: <EMAIL>
    sender-name: XO Team

elasticsearch:
  logging:
    enabled: true
    host: elastic.thexo.app
    username: ${ELASTICSEARCH_USER}
    password: ${ELASTICSEARCH_PASS}
    index: logs-xo-develop-%d{yyyy-MM-dd}
    connect-timeout: 5000
    read-timeout: 10000
    max-retries: 3