<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <title>Confirmed Reservation PDF</title>
    <th:block th:replace="~{inline/attachments/components/styles :: pdfStyle}"></th:block>
</head>
<body>
<div class="content">
    <div class="header">
        <img src="https://xo-test-bucket.s3.de.io.cloud.ovh.net/75b27b52-0572-437b-b5b3-379a76561665.png" alt="logo" width="54" height="54">
        <img src="https://xo-test-bucket.s3.de.io.cloud.ovh.net/13e7dac8-42bc-44c8-bf49-af6467539a03.png" alt="slogan" width="200" height="47">
    </div>

    <div class="main-content">
        <div class="section">
            <h2>Event Name</h2>
            <p th:text="${eventName}">Can be up to 100 characters maximum</p>
        </div>

        <div class="purple-line"></div>

        <div class="section">
            <h2>Where and When</h2>
            <p><span th:text="${venueName}">VenueName</span> on <span th:text="${dateTime}">Day, No Mon at 00:00</span></p>
        </div>

        <div class="purple-line"></div>

        <div class="section">
            <h2>Booking Name</h2>
            <p th:text="${firstName}">MasterOwner FullName</p>
        </div>

        <div class="purple-line"></div>

        <div class="section">
            <h2>Booking Number</h2>
            <p th:text="${bookingNumber}">Booking Number</p>
        </div>

        <div class="purple-line"></div>

        <div class="section">
            <h2>What's Included <i>(per single reservation)</i></h2>
            <p th:text="${includedDetails}">Access for min-max people. Includes €€ as credit to spend.</p>
        </div>

        <table width="100%" cellpadding="0" cellspacing="0" border="0"
               style="border: 4px solid #8C01F7; border-radius: 5px; max-width: 100%; margin: 20px auto;">
            <tr>
                <td style="padding: 15px;">
                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                        <tr>
                            <td width="100%" style="vertical-align: top;">
                                <h3 style="margin: 0 5px; font-size: 16px;">
                                    <span th:text="${quantity}">5</span> x
                                    <span th:text="${tierName}">Test Reservation tier title</span>
                                </h3>
                            </td>
                            <td width="30%" style="text-align: right; vertical-align: top;">
                                <p style="margin: 0; font-weight: bold;" th:text="${netAmount}">€000.00</p>
                                <p style="margin: 0; font-size: 14px;">(Deposit)</p>
                            </td>
                        </tr>
                    </table>

                    <!-- Reservation Details with fixed layout -->
                    <table width="85%" cellpadding="5" cellspacing="0" border="0" style="margin-top: 10px;">
                        <tr>
                            <td width="40%" style="color: black; border-bottom: 1px solid #CCCCCC;"><strong>Minimum Spend</strong></td>
                            <td style="border-bottom: 1px solid #CCCCCC;" th:text="${minimumSpend}">€000.00</td>
                        </tr>
                        <tr>
                            <td style="color: black; border-bottom:1px solid #CCCCCC"><strong>Party Size</strong></td>
                            <td style="border-bottom: 1px solid #CCCCCC" th:text="${partySize}">XX</td>
                        </tr>
                        <tr>
                            <td style="color: black; border-bottom: 1px solid #CCCCCC;"><strong>Occasion</strong></td>
                            <td style="border-bottom: 1px solid #CCCCCC;" th:text="${occasion}">Text</td>
                        </tr>
                        <tr>
                            <td style="color: black;"><strong>Special Requests</strong></td>
                            <td th:text="${specialRequests}">Special requests can be 100 charactersSpecial requests can be 100 charactersSpecial requests can be 100 charactersSpecial requests can be 100 characters Special requests can be 100 characters max characters max characters max characters max</td>
                        </tr>
                    </table>

                    <!-- Total Section -->
                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top: 20px;">
                        <tr>
                            <td style="border-bottom: 1px solid #8C01F7; border-top: 1px solid #8C01F7; font-weight: bold; font-size: 16px; padding: 5px 0 5px 0">Service Fee</td>
                            <td style="text-align: right; font-weight: bold; border-bottom: 1px solid #8C01F7; border-top: 1px solid #8C01F7;" th:text="${totalFee}">€000.00</td>
                        </tr>
                        <tr>
                            <td style="font-weight: bold; padding-top: 5px; font-size: 20px">Total Paid Online</td>
                            <td style="text-align: right; font-weight: bold; padding-top: 5px; font-size: 20px" th:text="${totalAmount}">€000.00</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</div>
</body>
</html>