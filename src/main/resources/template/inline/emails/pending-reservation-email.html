<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="initial-scale=1, width=device-width"/>
    <title>Reservation Pending</title>
</head>
<body style="font-family: Helvetica, Arial, Verdana, sans-serif; line-height: 1.6; margin: 0; padding: 0; background-color: #f4f4f4; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;">
<table cellpadding="0" cellspacing="0" border="0" width="100%"
       style="color: black; font-family: Helvetica, Arial, Verdana, sans-serif;">
    <tr>
        <td align="center">
            <!-- Main Container -->
            <table cellpadding="0" cellspacing="0" border="0" width="600"
                   style="width: 600px; max-width: 100%; margin: 0 auto; background: #ffffff;">
                <tr>
                    <td>
                        <!-- Header -->
                        <th:block th:replace="~{inline/components/header :: header}"></th:block>

                        <!-- Introduction -->
                        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="padding: 20px;">
                            <tr>
                                <td>
                                    <p style="margin: 10px 0;">Hi <span th:text="${firstName}">Guest</span>,</p>
                                    <p style="margin: 10px 0;">Thank you for your reservation request for <strong style="" th:text="${eventName}">Event</strong>
                                        at <strong style="" th:text="${venueName}">Venue</strong>. Your request is
                                        currently pending to be accepted or declined from the venue/event organizer.</p>
                                    <p style="margin: 10px 0; ">We'll notify you as soon as there's news on your
                                        reservation request. Make sure in-app notifications are enabled so you don't miss any
                                        updates. You'll also receive an email with the next steps, including any
                                        required payment link. Please keep an eye on your junk or spam folders.</p>
                                </td>
                            </tr>
                        </table>

                        <!-- Reservation Details -->
                        <th:block th:replace="~{inline/components/reservation-details-box :: reservationDetailsBox(true, false)}"></th:block>

                        <!-- Sign off -->
                        <th:block th:replace="~{inline/components/contact-and-help-section :: signOff}"></th:block>

                        <!-- Footer -->
                        <th:block th:replace="~{inline/components/footer :: footer}"></th:block>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html>