<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<body>
<!--no used atm-->
<th:block th:fragment="ticketContainer">
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="max-width: 600px; margin: 0 auto;">
        <tr>
            <td style="border: 4px solid #8C01F7; border-radius: 5px; padding: 20px;">
                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                    <tr>
                        <td width="30%" style="vertical-align: top;">
                            <img th:src="@{'data:image/png;base64,'+${qrCodeBase64}}"
                                 alt="QR Code"
                                 width="187"
                                 height="177"
                                 style="display: block; border: 1px solid black"/>
                        </td>
                        <td width="70%" style="padding-left: 15px; vertical-align: top; text-align: left;">
                            <strong style="font-size: 16px; line-height: 1.4; display: block;"
                                    th:text="${tierName}">2 lines</strong>

                            <span style="font-size: 14px; display: block; margin-top: 5px;">
                                    Pass (<span th:text="${currentTicket}">A</span>) of (<span th:text="${totalTickets}">TotalPurchased</span>)
                                </span>
                            <br>
                            <strong style="font-size: 14px; display: block; margin-top: 10px;">
                                Paid Online: <span th:text="${ticketPrice}">€€</span>
                            </strong>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" style="padding-top: 15px;">
                                <span style="font-size: 14px; margin-left: 15px">
                                    <strong>Pass number: </strong>
                                    <span th:text="${ticketReferenceNumber}">NRTASMLL</span>
                                </span>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</th:block>
</body>
</html>