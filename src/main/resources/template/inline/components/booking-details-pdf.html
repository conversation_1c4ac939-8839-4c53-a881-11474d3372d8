<!-- Event Details Component -->
<div th:fragment="bookingDetails(showBookingN<PERSON>ber, showBookingName, showRequestedBy, showIncludedDetails, showBookedBy, showTicketNumber, isReservation)">
    <table style="width: 100%; border-collapse: collapse; max-width: 600px; margin: auto;" width=100% cellpadding="0" cellspacing="0">
        <!-- Event Name Section -->
        <tr style="margin: 20px">
            <td style="padding-bottom: 15px;">
                <h2 style="margin: 0; font-size: 18px;">Event Name</h2>
                <p style="margin: 5px 0;" th:text="${eventName}">Can be up to 100 characters maximum</p>
            </td>
        </tr>

        <!-- Where and When Section -->
        <tr>
            <td style="padding-bottom: 15px;">
                <div style="width: 150px; border-top: 2px solid #8A2BE2; margin-bottom: 10px;"></div>
                <h2 style="margin: 0; font-size: 18px;">Where and When</h2>
                <p style="margin: 5px 0;">
                    <span th:text="${venueName}">VenueName</span> on
                    <span th:text="${dateTime}">Day, No Mon at 00:00</span>
                </p>
            </td>
        </tr>

        <!-- Booking Name -->
        <tr th:if="${showBookingName}">
            <td style="padding-bottom: 15px;">
                <div style="width: 150px; border-top: 2px solid #8A2BE2; margin-bottom: 10px;"></div>
                <h2 style="margin: 0; font-size: 18px;">Booking Name</h2>
                <p style="margin: 5px 0;" th:text="${bookingName}">Booking Name</p>
            </td>
        </tr>

        <!-- Booked By -->
        <tr th:if="${showBookedBy}">
            <td style="padding-bottom: 15px;">
                <div style="width: 150px; border-top: 2px solid #8A2BE2; margin-bottom: 10px;"></div>
                <h2 style="margin: 0; font-size: 18px;">Booked by</h2>
                <p style="margin: 5px 0;" th:text="${fullName}">full name</p>
            </td>
        </tr>

        <!-- Booking Number -->
        <tr th:if="${showBookingNumber}">
            <td style="padding-bottom: 15px;">
                <div style="width: 150px; border-top: 2px solid #8A2BE2; margin-bottom: 10px;"></div>
                <h2 style="margin: 0; font-size: 18px;">Booking Number</h2>
                <p style="margin: 5px 0;" th:text="${bookingNumber}">Booking Number</p>
            </td>
        </tr>

        <!-- # of Tickets -->
        <tr th:if="${showTicketNumber}">
            <td style="padding-bottom: 15px;">
                <div style="width: 150px; border-top: 2px solid #8A2BE2; margin-bottom: 10px;"></div>
                <h2 style="margin: 0; font-size: 18px;">Number of Tickets</h2>
                <p style="margin: 5px 0;" th:text="${quantity}">Z</p>
            </td>
        </tr>

        <!-- Requested By -->
        <tr th:if="${showRequestedBy}">
            <td style="padding-bottom: 15px;">
                <div style="width: 150px; border-top: 2px solid #8A2BE2; margin-bottom: 10px;"></div>
                <h2 style="margin: 0; font-size: 18px;">Requested By</h2>
                <p style="margin: 5px 0;" th:text="${fullName}">Requester Name</p>
            </td>
        </tr>

        <!-- What's Included -->
        <tr th:if="${showIncludedDetails}">
            <td style="padding-bottom: 15px;">
                <div style="width: 150px; border-top: 2px solid #8A2BE2; margin-bottom: 10px;"></div>
                <h2 style="margin: 0; font-size: 18px;">
                    What's Included
                    <span th:if="${isReservation}">(per single reservation)</span>
                    <span th:unless="${isReservation}">(per single ticket)</span>
                </h2>
                <p style="margin: 5px 0;" th:text="${includedDetails}">Includes access details</p>
            </td>
        </tr>
    </table>
</div>