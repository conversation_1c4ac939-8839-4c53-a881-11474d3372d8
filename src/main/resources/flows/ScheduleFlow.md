# Schedules

## Description of the Problem

- A single event can have multiple schedules. These schedules are dates and times when an event is operational. 
- When an event is created event schedules are created with it. 
- When booking tiers are created you can choose from a list of the event schedules to attach to them.
- You can add multiple event schedules to a single booking tier.
- You can choose if all the schedules will be valid for that tier or if the user will have to choose a number of them
- Maybes (need confirmation):
  - A booking tier of type ticket can have multiple schedules attached to it
  - A booking tier of type reservation can have only a single schedule active


### Known use cases:
- A festival spanning a weekend, where you can get different tickets specific day or weekend pass. These tickets are often different prices.
    - E.g. Friday £10, Saturday £20, weekend pass £25. Ticket can be validated for the specific schedule it was purchased for
    - Venue specifies the schedules for each ticket. User cannot change or select schedules
- A singer comes to a venue for a month. The venue creates a single event with multiple schedules.
  - The venue owner creates a booking tier which has all the available schedules, but the booking is only valid for a single schedule

## Ticket Validation
When creating a ticket for a booking, the ticket contains the schedules which are available for the tier.
For example ticket is valid for Friday, Saturday, Sunday. 3 items populated in ticket_schedules table. 
When door man wants to validate a ticket they should choose Event and current schedule they want to verify.
When ticket with schedule matching verification is presented, the ticket is validated and marked as used for specific schedule.

**Q**: What happens when you want a single ticket to be available for any of the days (e.g. a one-day pass in a three-day event)?

**Q**: A booking tier schedule is a subset of event schedule. A booking is linked to the booking tier, so a booking includes all the schedules the tier has. What if we want only one schedule to be present in the actual booking

#### Potential Solutions
1. Ticket contains schedules for all days. But we need a way to differentiate between tickets which can be used once, and tickets which can be used multiple times. 
   1. Add two fields to ticket table: MULTIPLE_USAGE and USED. both boolean fields. 
        - If multiple usage is true then check ticket for specific schedule. If not used for specific schedule then validate and allow entry, 
           change "used" field to true inside ticket_schedule table
        - If multiple usage is false then that means the ticket can be used in any of the specified schedules, but only once.
          1. Check used field in ticket table
          2. If true 
             1. Decline entry (ticket was already used)
          3. If false
             1. Check event schedules for current schedule
             2. if exists validate ticket, change "used" field in ticket to true
2. Instead of ticket having all schedules, it can have only the ones which are relevant to it
   - The user must choose one of those schedules for their booking 
   - Add field in booking_tier: schedule_choice-> boolean to differentiate if tier includes all schedules or the option to choose
   - **SubQuestion**: Where will chosen schedules be saved? The booking is linked to the booking tier, and the booking tier has all available schedules 
     - Can have a separate table called booking schedules which is a subset to booking_tier_schedules


### Issues with booking

1. A user can make a single booking per event. What happens when a user wants to make multiple bookings for an event but for different schedules. 
Maybe wants to go to a specific event multiple times. (e.g. to see a specific singer).
2. If you create a reservation for day 1, will be able to make another reservation for day 2? 
3. how is ordering handled in the event of multiple schedules in a single reservation? 
4. What happens when user books different things in different schedules but same booking?

## Discussed Solution:
- In the case of a reoccurring event (e.g. same singer for multiple days) the venue should create an event for each instance of the event
    - That means in the event level there are no schedules
- In the case of multi-day event (e.g. festivals over a weekend)
    - The venue should specify when the booking tier is valid (using valid_from, valid_until fields in db)
    - A booking tier can be valid for multiple ranges (a list of ranges can be provided)
    - The ticket will be valid for the ranges in the booking_tier
    - In a multi schedule booking tier the customer can scan the ticket once per schedule