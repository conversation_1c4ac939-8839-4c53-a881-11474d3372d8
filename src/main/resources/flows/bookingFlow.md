# Create NEW Booking Flow

1. User in Event Details clicks book
2. FE make request using userId & eventId (GET bookings/draft/{eventId}/{userId}/)
    1. if user doesn't have booking completed booking proceed else show error (issue, cant use same endpoint as get
       bookings)
    2. if user has booking in DRAFT status load that else proceed
3. CASE: NO BOOKING
    1. user selects what they want to add to their booking
   2. F<PERSON> sends post call to create booking (userId, eventId)
    2. once user clicks on checkout button request gets sent to BE (list of booking items)
    3. BE Validations:
        1. maximum number of tickets (from properties)
        2. no mixing of reservation and tickets
    4. BE creates booking entity with status DRAFT
    5. <PERSON><PERSON> creates booking item entity for each item (maybe this should be done in a separate step in a PUT call)
    6. Return booking back to FE
4. CASE: DRAFT BOOKING FOUND
    1. load booking
    2. user selects what they want to add to booking
    3. FE sends put call to update booking
    4. Regular validation
    5. set new values (maybe need to fetch old values and change them)

1. GET /booking/userId/eventId/check -> get if user has draft booking in specific event
2. POST /booking/ -> create booking with minimal fields
3. PUT /booking/{bookingId}/items -> add items to booking
4. PUT /booking/{bookingId} -> fill in rest of booking details
5. PUT /booking/{bookingId}/publish -> update booking status
6. 

## Endpoints

### GET
- GET /bookings/users/{userId} -> get all bookings for a user
- GET /bookings/events/{eventId} -> get all bookings for an event
- GET /bookings/{bookingId} -> get a specific booking (maybe should be booking_number)
- GET /bookings/{userId}/{eventId}/check -> get if user has draft booking in specific event (returns error if user has
  booking with bookingStatus != DRAFT else return booking)

### POST
- POST /bookings/{eventId} -> create booking in an event without any items
- POST /bookings/{bookingId}/items -> add new items to booking
- 

### PUT
- PUT /bookings/items/{bookingId) -> update a booking (used to add items to a booking)
