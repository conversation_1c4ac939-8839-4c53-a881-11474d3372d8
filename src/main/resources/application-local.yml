server:
  port: 8080

spring:
  mail:
    host: smtp.gmail.com            # SMTP server
    port: 587                       # Port (for TLS/STARTTLS)
    username: <EMAIL>   # Your email
    password: your-email-password    # Your email password
    properties:
      mail:
        smtp:
          auth: true                # Enable authentication
          starttls:
            enable: true            # Enable STARTTLS

  datasource:
    url: *****************************************
    username: postgres
    password: mysecretpassword
    driver-class-name: org.postgresql.Driver

  jpa:
    show-sql: false
    database: postgresql
    properties:
      hibernate:
        jdbc:
          time_zone: UTC
        format_sql: false
        default_schema: xo

  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master-local.yaml
    database-change-log-table: DATABASECHANGELOG
    database-change-log-lock-table: DATABASECHANGELOGLOCK

  cloud:
    aws:
      ses:
        region: eu-north-1
        access-key: ${AWS_SES_USER}
        secret-key: ${AWS_SES_PASSWORD}
    contabo:
      object-store:
        endpoint: https://eu2.contabostorage.com
        bucket-prefix: 6b90789d810d4d6b8273e135486f6680
        region: eu-central-1
        bucket: xo-dev-media
        access-key: ${CONTABO_ACCESS_KEY} # test values
        secret-key: ${CONTABO_SECRET_KEY} # test values
    ovh:
      object-store:
        endpoint: https://s3.de.io.cloud.ovh.net
        region: de
        bucket: xo-test-bucket
        access-key: ${OVH_ACCESS_KEY} # test values
        secret-key: ${OVH_SECRET_KEY} # test values
    s3mock:
      object-store:
        endpoint: http://localhost:9090
        region: eu-central-1
        bucket: mybucket
        access-key: test
        secret-key: test

  thymeleaf:
    prefix: classpath:/template/
    suffix: .html
    cache: false
    encoding: UTF-8

app:
  clients:
    go:
      basePath: https://xo.testgodev.nl
      api-key: ${GO_API_KEY} # need to populate locally
    email:
      provider: ses # Possible values: java-mail, ses
    payment:
      stripe:
        secret-key: ${STRIPE_SECRET_KEY} # test-mode-value
        connect-endpoint-secret: ${STRIPE_CONNECT_ENDPOINT_SECRET} # test-mode-value
        enabled: true
    media:
      provider: ovh
    notifications:
      firebase:
        credentials-b64: ${FIREBASE_CONFIG}

logging:
  level:
    sql: off
    org.hibernate.orm.jdbc.bind: OFF
# uncomment to check auth errors
#logging:
#  level:
#    org:
#      springframework:
#        security: TRACE