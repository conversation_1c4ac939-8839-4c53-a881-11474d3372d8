package com.xo.marketplace.controller;


import com.xo.backend.model.dto.EventVenueDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.entity.CountryCode;
import com.xo.marketplace.service.MarketPlaceVenueDiscoveryService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/marketplace/venues")
public class MarketPlaceVenueDiscoveryController {

    private final MarketPlaceVenueDiscoveryService marketPlaceVenueDiscoveryService;

    @GetMapping("/browse")
    public ResponseEntity<Page<VenueOverviewDTO>> getVisibleVenues(@RequestParam(value = "countryCode",required = false) CountryCode countryCode, Pageable pageable) {
        return ResponseEntity.ok(marketPlaceVenueDiscoveryService.getVisibleMarketPlaceVenuePage(countryCode,pageable));
    }

    @GetMapping("/{venueId}/events/upcoming")
    public ResponseEntity<Page<EventVenueDTO>> getVisibleUpcomingVenueEvents(@PathVariable String venueId, Pageable pageable) {
        return ResponseEntity.ok(marketPlaceVenueDiscoveryService.getVisibleMarketPlaceVenueEvents(venueId, pageable));
    }
}
