package com.xo.marketplace.controller;

import com.xo.marketplace.service.MarketPlaceEventConfigService;
import com.xo.marketplace.dto.MarketPlaceEventConfigDTO;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/marketplace/events/config")
public class MarketPlaceEventConfigController {

    private final MarketPlaceEventConfigService marketPlaceEventConfigService;

    @GetMapping("/{eventId}")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public MarketPlaceEventConfigDTO getMarketPlaceConfigById(@PathVariable("eventId") Integer eventId) {
        return marketPlaceEventConfigService.getMarketPlaceEventConfigByEventId(eventId);
    }

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public List<MarketPlaceEventConfigDTO> getMarketPlaceConfigList() {
        return marketPlaceEventConfigService.getAllMarketPlaceEventConfigs();
    }

    @PostMapping("/batch")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public List<MarketPlaceEventConfigDTO> createOrUpdateBatchMarketPlaceEventConfig(@RequestBody @Size(min = 1, max = 50)
                                                                                         List<MarketPlaceEventConfigDTO> marketPlaceEventConfigDTO) {
        return marketPlaceEventConfigService.createOrUpdateBatchMarketPlaceEventConfig(marketPlaceEventConfigDTO);
    }

    @DeleteMapping("/{eventId}")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public void deleteMarketPlaceConfig(@PathVariable("eventId") Integer eventId) {
        marketPlaceEventConfigService.deleteMarketPlaceEventConfigById(eventId);
    }

}
