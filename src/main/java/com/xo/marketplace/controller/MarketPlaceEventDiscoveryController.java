package com.xo.marketplace.controller;

import com.xo.backend.model.dto.EventBrowseDTO;
import com.xo.marketplace.database.entity.CountryCode;
import com.xo.marketplace.service.MarketplaceEventDiscoveryService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/marketplace/events")
public class MarketPlaceEventDiscoveryController {

    private final MarketplaceEventDiscoveryService marketplaceEventDiscoveryService;

    @GetMapping("/browse")
    public Page<EventBrowseDTO> getVisibleUpcomingEvents( @RequestParam(value = "countryCode",required = false) CountryCode countryCode, Pageable pageable) {
        return marketplaceEventDiscoveryService.getMarketplaceEvents(countryCode,pageable);
    }
}