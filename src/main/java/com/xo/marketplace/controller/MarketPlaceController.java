package com.xo.marketplace.controller;

import com.xo.marketplace.database.entity.CountryCode;
import com.xo.marketplace.dto.CountryDTO;
import com.xo.marketplace.service.MarketPlaceService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;


@RestController
@RequestMapping("/v1/marketplace")
@RequiredArgsConstructor
public class MarketPlaceController {

    private final MarketPlaceService marketPlaceService;


    @GetMapping("/supported-countries")
    public List<CountryDTO> getISOCountries(){
        return Arrays.stream(CountryCode.values())
                .map(country -> new CountryDTO(country.name(), country.getName()))
                .toList();
    }

    @PostMapping("/migrate-country-iso")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public void migrateCountryIso(){
        marketPlaceService.migrateCountryIso();
    }
}
