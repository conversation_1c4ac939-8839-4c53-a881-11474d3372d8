package com.xo.marketplace.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.math.BigDecimal;

@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public record VenueFeeConfigDTO(
        String venueId,

        @NotNull(message = "is required when feeConfig is provided")
        BigDecimal appFeePerc,

        @NotNull(message = "is required when feeConfig is provided")
        BigDecimal appFeeFixedPerTransaction,

        @NotNull(message = "is required when feeConfig is provided")
        BigDecimal appFeeFixedPerEntryPass,

        BigDecimal appFeeMin,
        BigDecimal appFeeMax,

        @NotNull(message = "is required when feeConfig is provided")
        BigDecimal appFeeToCustomerPerc,

        @NotNull(message = " is required when feeConfig is provided")
        BigDecimal pspFeePerc,

        @NotNull(message = "is required when feeConfig is provided")
        BigDecimal pspFeeFixed,

        @NotNull(message = "is required when feeConfig is provided")
        Boolean passPspFees
) {
}
