package com.xo.marketplace.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public record MarketPlaceVenueConfigDTO(
        Integer id,
        @NotNull String venueId,
        @NotNull Boolean visible,
        String countryIso,
        @Valid
        VenueFeeConfigDTO feeConfig
) {
}
