package com.xo.marketplace.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.mappers.EventDetailsMapper;
import com.xo.backend.model.dto.EventBrowseDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.entity.CountryCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.xo.backend.utlis.VenueUtils.safeParseVenueId;

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketplaceEventDiscoveryService {

    private final EventRepository eventRepository;
    private final VenueInfoService venueInfoService;
    private final EventDetailsMapper eventDetailsMapper;

    @Transactional(readOnly = true)
    public Page<EventBrowseDTO> getMarketplaceEvents(CountryCode countryCode,Pageable pageable) {
        Page<EventEntity> events;
        if(countryCode != null && CountryCode.EMPTY!=countryCode) {
            events=eventRepository.findMarketplaceEventsByCountryIso(countryCode.name(), pageable);
        }else {
            events = eventRepository.findMarketplaceEvents(pageable);
        }

        if (events.isEmpty()) {
            log.warn("No marketplace events found for the given pageable: {}", pageable);
            return Page.empty(pageable);
        }

        Set<Long> validVenueIds = events.stream()
                .map(event -> safeParseVenueId(event.getVenueId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, VenueOverviewDTO> venueMap = venueInfoService.getVenuesOverview(validVenueIds);

        return events.map(event -> {
            Long venueId = safeParseVenueId(event.getVenueId());
            VenueOverviewDTO venue = venueId != null ? venueMap.get(venueId) : null;

            if (venue == null) {
                log.warn("No venue info found for event with ID: {}", event.getId());
            }

            return eventDetailsMapper.mapToEventsBrowserDTO(event, venue);
        });
    }
}