package com.xo.marketplace.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.error.exceptions.MarketPlaceEventConfigNotFoundException;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.entity.MarketPlaceEventConfig;
import com.xo.marketplace.dto.MarketPlaceEventConfigDTO;
import com.xo.marketplace.mapper.MarketPlaceEventConfigMapper;
import com.xo.marketplace.database.repository.MarketPlaceEventConfigRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MarketPlaceEventConfigService {

    private final MarketPlaceEventConfigRepository marketPlaceEventConfigRepository;
    private final MarketPlaceEventConfigMapper marketPlaceEventConfigMapper;
    private final EventRepository eventRepository;
    private final VenueInfoService venueInfoService;

    @Transactional
    public MarketPlaceEventConfigDTO getMarketPlaceEventConfigByEventId(Integer eventId) {
        return marketPlaceEventConfigRepository.findByEventId(eventId)
                .map(marketPlaceEventConfigMapper::toMarketPlaceEventConfigDTO)
                .orElseThrow(MarketPlaceEventConfigNotFoundException::new);
    }

    @Transactional
    public List<MarketPlaceEventConfigDTO> getAllMarketPlaceEventConfigs() {
        return marketPlaceEventConfigRepository.findAll().stream()
                .map(marketPlaceEventConfigMapper::toMarketPlaceEventConfigDTO)
                .toList();
    }

    @Transactional
    public void deleteMarketPlaceEventConfigById(Integer eventId) {
        marketPlaceEventConfigRepository.deleteByEventId(eventId);
    }

    @Transactional
    public List<MarketPlaceEventConfigDTO> createOrUpdateBatchMarketPlaceEventConfig(List<MarketPlaceEventConfigDTO> configDTOs) {

        Map<Integer, MarketPlaceEventConfigDTO> latestConfigs = configDTOs.stream()
                .collect(Collectors.toMap(
                        MarketPlaceEventConfigDTO::eventId,
                        config -> config,
                        (existing, replacement) -> replacement  // Keep the last occurrence
                ));

        Map<Integer, MarketPlaceEventConfig> existingConfigs = marketPlaceEventConfigRepository
                .findAllByEventIdIn(latestConfigs.keySet()).stream()
                .collect(Collectors.toMap(MarketPlaceEventConfig::getEventId, config -> config));

        // Identify new config event IDs
        Set<Integer> newConfigEventIds = latestConfigs.keySet().stream()
                .filter(eventId -> !existingConfigs.containsKey(eventId))
                .collect(Collectors.toSet());

        // Get events and venues only for new configs
        Map<Integer, EventEntity> events = newConfigEventIds.isEmpty() ? Map.of() :
                eventRepository.findAllById(newConfigEventIds).stream()
                        .collect(Collectors.toMap(EventEntity::getId, event -> event));

        Map<Long, VenueOverviewDTO> venues = events.isEmpty() ? Map.of() :
                venueInfoService.getVenuesOverview(
                        events.values().stream()
                                .map(event -> Long.valueOf(event.getVenueId()))
                                .collect(Collectors.toSet())
                );

        List<MarketPlaceEventConfig> configsToSave = latestConfigs.values().stream()
                .map(dto -> {
                    // For existing configs, just update visibility
                    if (existingConfigs.containsKey(dto.eventId())) {
                        MarketPlaceEventConfig config = existingConfigs.get(dto.eventId());
                        config.setVisible(dto.visible());
                        return config;
                    }

                    // For new configs, validate event and venue data
                    EventEntity event = events.get(dto.eventId());
                    if (event == null) {
                        return null;
                    }

                    VenueOverviewDTO venue = venues.get(Long.valueOf(event.getVenueId()));
                    if (venue == null) {
                        return null;
                    }

                    // Create new config only if we have all required data
                    return MarketPlaceEventConfig.builder()
                            .eventId(dto.eventId())
                            .visible(dto.visible())
                            .countryIso(venue.companyAddress().country().code())
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();

        return marketPlaceEventConfigRepository.saveAll(configsToSave).stream()
                .map(marketPlaceEventConfigMapper::toMarketPlaceEventConfigDTO)
                .toList();
    }

}
