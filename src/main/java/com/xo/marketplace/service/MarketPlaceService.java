package com.xo.marketplace.service;


import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.error.exceptions.EventNotFoundException;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.entity.MarketPlaceEventConfig;
import com.xo.marketplace.database.entity.MarketPlaceVenueConfig;
import com.xo.marketplace.database.repository.MarketPlaceEventConfigRepository;
import com.xo.marketplace.database.repository.MarketPlaceVenueConfigRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MarketPlaceService {

    private final MarketPlaceEventConfigRepository marketPlaceEventConfigRepository;
    private final MarketPlaceVenueConfigRepository marketPlaceVenueConfigRepository;
    private final EventRepository eventRepository;
    private final VenueInfoService venueInfoService;

    private final int BATCH_SIZE = 50;

    @Transactional
    public void migrateCountryIso() {
        List<MarketPlaceVenueConfig> venueConfigList = marketPlaceVenueConfigRepository.findByCountryIsoIsNull();
        List<MarketPlaceEventConfig> eventConfigList = marketPlaceEventConfigRepository.findByCountryIsoIsNull();

        Map<Integer, Long> eventToVenueMap = getEventToVenueMap(eventConfigList);
        Set<Long> venueIds = getVenueIds(venueConfigList, eventToVenueMap);

        Map<Long, VenueOverviewDTO> venueOverviewMap = getVenueOverviewMap(venueIds);

        updateVenueConfigCountryIso(venueConfigList, venueOverviewMap);
        updateEventConfigCountryIso(eventConfigList, eventToVenueMap, venueOverviewMap);
    }

    private Map<Integer, Long> getEventToVenueMap(List<MarketPlaceEventConfig> eventConfigList) {
        Map<Integer, Long> eventToVenueMap = new HashMap<>();

        eventConfigList.forEach(eventConfig -> {
            Integer eventId = eventConfig.getEventId();
            Long venueId = getVenueIdForEvent(eventId);
            eventToVenueMap.put(eventId, venueId);
        });

        return eventToVenueMap;
    }

    private Long getVenueIdForEvent(Integer eventId) {
        return eventRepository.findById(eventId)
                .map(event -> Long.parseLong(event.getVenueId()))
                .orElseThrow(EventNotFoundException::new);
    }

    private Set<Long> getVenueIds(List<MarketPlaceVenueConfig> venueConfigList, Map<Integer, Long> eventToVenueMap) {
        Set<Long> venueIds = venueConfigList.stream()
                .map(venueConfig -> Long.parseLong(venueConfig.getVenueId()))
                .collect(Collectors.toSet());

        venueIds.addAll(eventToVenueMap.values());

        return venueIds;
    }

    private Map<Long, VenueOverviewDTO> getVenueOverviewMap(Set<Long> venueIds) {
        Map<Long, VenueOverviewDTO> venueOverviewMap = new HashMap<>();
        List<Long> currentBatch = new ArrayList<>(BATCH_SIZE);

        for (Long venueId : venueIds) {
            currentBatch.add(venueId);

            if (currentBatch.size() == BATCH_SIZE) {
                venueOverviewMap.putAll(venueInfoService.getVenuesOverview(currentBatch));
                currentBatch.clear();
            }
        }

        if (!currentBatch.isEmpty()) {
            venueOverviewMap.putAll(venueInfoService.getVenuesOverview(currentBatch));
        }

        return venueOverviewMap;
    }

    private void updateVenueConfigCountryIso(List<MarketPlaceVenueConfig> venueConfigList, Map<Long, VenueOverviewDTO> venueOverviewMap) {
        venueConfigList.forEach(venueConfig -> {
            Long venueId = Long.parseLong(venueConfig.getVenueId());
            String countryIso = getCountryIso(venueOverviewMap, venueId);
            venueConfig.setCountryIso(countryIso);
        });
    }

    private void updateEventConfigCountryIso(List<MarketPlaceEventConfig> eventConfigList, Map<Integer, Long> eventToVenueMap, Map<Long, VenueOverviewDTO> venueOverviewMap) {
        eventConfigList.forEach(eventConfig -> {
            Long venueId = eventToVenueMap.get(eventConfig.getEventId());
            String countryIso = getCountryIso(venueOverviewMap, venueId);
            eventConfig.setCountryIso(countryIso);
        });
    }

    private String getCountryIso(Map<Long, VenueOverviewDTO> venueOverviewMap, Long venueId) {
        VenueOverviewDTO venueOverviewDTO = venueOverviewMap.get(venueId);
        return venueOverviewDTO != null ? venueOverviewDTO.companyAddress().country().code() : null;
    }

}
