package com.xo.marketplace.database.repository;

import com.xo.marketplace.database.entity.MktVenueFeeConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MktVenueFeeConfigRepository extends JpaRepository<MktVenueFeeConfigEntity, Integer> {

    Optional<MktVenueFeeConfigEntity> findByVenueId(String venueId);
}
