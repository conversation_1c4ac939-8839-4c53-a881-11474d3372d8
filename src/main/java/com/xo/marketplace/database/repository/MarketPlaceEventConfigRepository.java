package com.xo.marketplace.database.repository;

import com.xo.marketplace.database.entity.MarketPlaceEventConfig;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface MarketPlaceEventConfigRepository extends JpaRepository<MarketPlaceEventConfig, Integer> {

    Optional<MarketPlaceEventConfig> findByEventId(Integer eventId);

    List<MarketPlaceEventConfig> findByCountryIsoIsNull();

    void deleteByEventId(Integer eventId);

    List<MarketPlaceEventConfig> findAllByEventIdIn(Set<Integer> allEventIds);
}
