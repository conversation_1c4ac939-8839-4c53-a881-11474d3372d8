package com.xo.marketplace.database.repository;

import com.xo.marketplace.database.entity.MarketPlaceVenueConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface MarketPlaceVenueConfigRepository extends JpaRepository<MarketPlaceVenueConfig, Integer> {

    Optional<MarketPlaceVenueConfig> findByVenueId(String venueId);

    List<MarketPlaceVenueConfig> findByCountryIsoIsNull();

    Page<MarketPlaceVenueConfig> findByVisibleTrue(Pageable pageable);

    Page<MarketPlaceVenueConfig> findByVisibleTrueAndCountryIso(String countryIso,Pageable pageable);

    void deleteByVenueId(String venueId);

    boolean existsByVenueIdAndVisibleTrue(String venueId);

    List<MarketPlaceVenueConfig> findAllByVenueIdIn(Collection<String> allVenueIds);
}
