package com.xo.marketplace.database.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToOne;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@RequiredArgsConstructor
public class MarketPlaceVenueConfig extends MarketPlaceConfig {

    @Column(name = "venue_id", unique = true)
    private String venueId;

    @OneToOne(mappedBy = "marketPlaceVenueConfig", cascade = CascadeType.ALL)
    private MktVenueFeeConfigEntity feeConfig;

}
