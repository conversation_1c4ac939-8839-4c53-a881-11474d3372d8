package com.xo.marketplace.repository;

import com.xo.marketplace.database.MarketPlaceEventConfig;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface MarketPlaceEventConfigRepository extends JpaRepository<MarketPlaceEventConfig, Integer> {

    Optional<MarketPlaceEventConfig> findByEventId(Integer eventId);

    List<MarketPlaceEventConfig> findByCountryIsoIsNull();

    void deleteByEventId(Integer eventId);

    List<MarketPlaceEventConfig> findAllByEventIdIn(Set<Integer> allEventIds);
}
