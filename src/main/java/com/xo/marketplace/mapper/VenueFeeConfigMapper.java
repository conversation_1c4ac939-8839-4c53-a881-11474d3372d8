package com.xo.marketplace.mapper;

import com.xo.marketplace.database.entity.MktVenueFeeConfigEntity;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface VenueFeeConfigMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "marketPlaceVenueConfig", ignore = true)
    MktVenueFeeConfigEntity toFeeConfigEntity(VenueFeeConfigDTO venueFeeConfigDTO);

    VenueFeeConfigDTO toVenueFeeConfigDTO(MktVenueFeeConfigEntity feeConfigEntity);
}
