package com.xo.marketplace.mapper;

import com.xo.marketplace.database.entity.MarketPlaceVenueConfig;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = VenueFeeConfigMapper.class)
public interface MarketPlaceVenueConfigMapper {

    @Mapping(target = "feeConfig", source = "feeConfig")
    MarketPlaceVenueConfigDTO toMarketPlaceVenueConfigDTO(MarketPlaceVenueConfig marketPlaceVenueConfig);
}
