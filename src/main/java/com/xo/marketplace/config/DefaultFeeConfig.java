package com.xo.marketplace.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Data
@Component
@ConfigurationProperties(prefix = "app.fees.default")
public class DefaultFeeConfig {
    
    private BigDecimal appFeePerc = new BigDecimal("0.00");
    private BigDecimal appFeeFixedPerTransaction = new BigDecimal("1.00");
    private BigDecimal appFeeFixedPerEntryPass = new BigDecimal("1.50");
    private BigDecimal appFeeMin = new BigDecimal("1.50");
    private BigDecimal appFeeMax = new BigDecimal("25.00");
    private BigDecimal appFeeToCustomerPerc = new BigDecimal("1.00");
    private BigDecimal pspFeePerc = new BigDecimal("0.00");
    private BigDecimal pspFeeFixed = new BigDecimal("0.00");
    private Boolean passPspFees = false;
}
