package com.xo.marketplace.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Data
@Component
@ConfigurationProperties(prefix = "marketplace.fees.default")
public class MarketplaceDefaultFeeConfig {
    
    private BigDecimal appFeePerc;
    private BigDecimal appFeeFixedPerTransaction;
    private BigDecimal appFeeFixedPerEntryPass;
    private BigDecimal appFeeMin;
    private BigDecimal appFeeMax;
    private BigDecimal appFeeToCustomerPerc;
    private BigDecimal pspFeePerc;
    private BigDecimal pspFeeFixed;
    private Boolean passPspFees;
}
