package com.xo.backend.controller;

import com.xo.backend.database.service.DeviceTokenService;
import com.xo.backend.model.dto.notifications.*;
import com.xo.backend.utlis.SecurityContextUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/device-tokens")
public class DeviceTokenController {

    private final DeviceTokenService deviceTokenService;

    @PostMapping //upsert method
    public ResponseEntity<DeviceTokenResponse> registerDeviceToken(@Valid @RequestBody UpsertDeviceTokenRequest request) {

        Integer userId = SecurityContextUtil.getCurrentUserId();

        DeviceTokenResponse response = deviceTokenService.registerOrUpdateDeviceToken(userId, request.deviceToken(),
                request.deviceOs(), request.deviceName(), request.tokenExpiry());

        return ResponseEntity.ok(response);
    }

    @PatchMapping("/status")
    public ResponseEntity<DeviceTokenResponse> updateDeviceTokenStatus(@Valid @RequestBody UpdateDeviceTokenStatusRequest request) {

        Integer userId = SecurityContextUtil.getCurrentUserId();

        DeviceTokenResponse response = deviceTokenService.updateTokenActiveStatus(userId, request.deviceToken(), request.isActive());

        return ResponseEntity.ok(response);
    }

    @PatchMapping("/preferences")
    public ResponseEntity<DeviceTokenResponse> updateDeviceTokenPreferences(@Valid @RequestBody UpdateDeviceTokenPreferencesRequest request) {
        Integer userId = SecurityContextUtil.getCurrentUserId();

        DeviceTokenResponse response = deviceTokenService.updateTokenPushEnabled(userId, request.deviceToken(), request.pushEnabled());

        return ResponseEntity.ok(response);
    }

    @DeleteMapping
    public ResponseEntity<Void> deleteDeviceToken(@Valid @RequestBody DeleteDeviceTokenRequest request) {
        Integer userId = SecurityContextUtil.getCurrentUserId();

        deviceTokenService.deleteToken(userId, request.deviceToken());
        return ResponseEntity.ok().build();
    }

}
