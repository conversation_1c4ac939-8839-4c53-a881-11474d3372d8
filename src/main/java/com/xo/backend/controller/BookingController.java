package com.xo.backend.controller;


import com.xo.backend.database.entity.bookings.PaymentStatus;
import com.xo.backend.model.dto.DetailedBookingDTO;
import com.xo.backend.model.dto.request.booking.AddItemsToBookingRequest;
import com.xo.backend.model.dto.request.booking.AdditionalBookingInfoRequest;
import com.xo.backend.model.dto.request.booking.BookingStatusUpdateResponseDTO;
import com.xo.backend.model.dto.request.booking.CreateBookingRequest;
import com.xo.backend.model.dto.responses.*;
import com.xo.backend.service.BookingService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/v1/bookings")
@RequiredArgsConstructor
public class BookingController {

    private final BookingService bookingService;

    @PostMapping //user - change
    public ResponseEntity<CreateBookingResponseDTO> createNewBooking(@Valid @RequestBody CreateBookingRequest createDraftBookingRequest) {

        CreateBookingResponseDTO result = bookingService.createNewBooking(createDraftBookingRequest);

        return ResponseEntity.ok(result);
    }


    @PutMapping(path = "/{bookingId}/items")//user
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<CreateBookingResponseDTO> addItemsToBooking(@PathVariable Integer bookingId,
                                                                      @Valid @RequestBody AddItemsToBookingRequest addItemsToBookingRequest) {

        CreateBookingResponseDTO result = bookingService.addItemsToBooking(bookingId, addItemsToBookingRequest);

        return ResponseEntity.ok(result);
    }

    @PutMapping(path = "/{bookingId}/additional-info")//user
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<CreateBookingResponseDTO> addInfoToBooking(@PathVariable Integer bookingId,
                                                                     @Valid @RequestBody AdditionalBookingInfoRequest additionalBookingInfoRequest) {

        CreateBookingResponseDTO result = bookingService.addInfoToBooking(bookingId, additionalBookingInfoRequest);

        return ResponseEntity.ok(result);
    }

    @GetMapping(path = "/{bookingId}/ticket-summary")//user, maybe deprecate
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<TicketSummaryResponseDTO> getTicketSummary(@PathVariable Integer bookingId) {
        TicketSummaryResponseDTO result = bookingService.getTicketSummary(bookingId);
        return ResponseEntity.ok(result);
    }

    @GetMapping(path = "/{bookingId}/reservation-summary") //user maybe deprecate
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<ReservationSummaryResponseDTO> getReservationSummary(@PathVariable Integer bookingId) {
        ReservationSummaryResponseDTO result = bookingService.getReservationSummary(bookingId);
        return ResponseEntity.ok(result);
    }

    @PostMapping(path = "/{bookingId}/validate")
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<BookingValidationDTO> validateBooking(@PathVariable Integer bookingId) {
       return ResponseEntity.ok(bookingService.validateBooking(bookingId));
    }

    @PatchMapping(path = "/{bookingId}/finalize") //user
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<BookingStatusUpdateResponseDTO> finalizeBooking(@PathVariable Integer bookingId) {
        BookingStatusUpdateResponseDTO result = bookingService.finalizeBooking(bookingId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/{bookingId}") //user
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<DetailedBookingDTO> getBookingDetails(@PathVariable int bookingId) {
        DetailedBookingDTO bookingDetailsList = bookingService.getBookingDetails(bookingId);
        return ResponseEntity.ok(bookingDetailsList);
    }

    @Deprecated
    @GetMapping //user
    public ResponseEntity<Page<BookingListResponse>> getAllBookingsForUser(Pageable pageable) {
        return ResponseEntity.ok(bookingService.getAllBookingsForUser(pageable));
    }


    @GetMapping("/upcoming")
    public ResponseEntity<Page<BookingListResponse>> getUpcomingBookingsForUser(Pageable pageable) {
        return ResponseEntity.ok(bookingService.getUpcomingBookingsForUser(pageable));
    }

    @GetMapping("/completed")
    public ResponseEntity<Page<BookingListResponse>> getCompletedBookingsForUser(Pageable pageable) {
        return ResponseEntity.ok(bookingService.getCompletedBookingsForUser(pageable));
    }

    @GetMapping("/{bookingId}/payment/status")
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<PaymentStatus> getCompletedBookingsForUser(@PathVariable("bookingId") Integer bookingId) {
        return ResponseEntity.ok(bookingService.getBookingPaymentStatus(bookingId));
    }

    @PostMapping("/{bookingId}/payments/expire-sessions")
    @PreAuthorize("hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<String> expireActivePaymentSessionsForBooking(@PathVariable Integer bookingId) {
        bookingService.expireBookingPaymentSessions(bookingId);
        return ResponseEntity.ok("All sessions expired successfully");
    }
}
