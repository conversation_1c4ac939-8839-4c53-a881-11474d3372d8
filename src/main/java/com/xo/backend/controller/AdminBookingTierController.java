package com.xo.backend.controller;


import com.xo.backend.controller.groups.CreateBookingTier;
import com.xo.backend.model.dto.BookingTierDTO;
import com.xo.backend.service.AdminBookingTierService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/venues/{venueId}")
@RequiredArgsConstructor
public class AdminBookingTierController {

    private final AdminBookingTierService bookingTierService;

    @PostMapping("/events/{eventId}/booking-tiers")
    @PreAuthorize("hasRole('ADMIN')  OR hasRole('PLATFORM_ADMIN')  OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<BookingTierDTO> createBookingTier(@PathVariable("venueId") String venueId,
                                                            @PathVariable("eventId") Integer eventId,
                                                            @Validated(CreateBookingTier.class) @RequestBody BookingTierDTO bookingTierDTO) {

        return ResponseEntity.ok(bookingTierService.createBookingTierEntity(venueId, eventId, bookingTierDTO));
    }

    @PutMapping("/booking-tiers/{bookingTierId}")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN')   OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<BookingTierDTO> updateBookingTier(@PathVariable("venueId") String venueId,
                                                            @PathVariable("bookingTierId") Integer bookingTierId,
                                                            @RequestBody BookingTierDTO bookingTierDTO) {

        return ResponseEntity.ok(bookingTierService.updateBookingTierEntity(venueId, bookingTierId, bookingTierDTO));
    }

    @DeleteMapping("/booking-tiers/{bookingTierId}")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN')   OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<String> deleteBookingTier(@PathVariable("venueId") String venueId,
                                                    @PathVariable("bookingTierId") Integer bookingTierId) {

        bookingTierService.deleteBookingTierEntity(venueId, bookingTierId);
        return ResponseEntity.ok("Booking Tier deleted successfully");
    }

    @GetMapping("/booking-tiers/{bookingTierId}")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN')   OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<BookingTierDTO> getBookingTier(@PathVariable("venueId") String venueId,
                                                         @PathVariable("bookingTierId") Integer bookingTierId) {

        return ResponseEntity.ok(bookingTierService.getBookingTierDetails(venueId, bookingTierId));
    }

    @GetMapping("/event/{eventId}/booking-tiers")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN')   OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<List<BookingTierDTO>> getEventBookingTiers(@PathVariable("venueId") String venueId,
                                                                     @PathVariable("eventId") Integer eventId) {

        return ResponseEntity.ok(bookingTierService.getEventBookingTiers(venueId, eventId));
    }
}
