package com.xo.backend.controller;

import com.xo.backend.client.payments.PaymentProvider;
import com.xo.backend.database.service.PaymentService;
import com.xo.backend.model.dto.responses.payments.CreateCheckoutResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/v1/bookings/{bookingId}/payments")
@RequiredArgsConstructor
public class PaymentController {

    private final PaymentService paymentService;

    @PostMapping("/checkout-session")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasPermission(#bookingId, 'BOOKING', 'OWNER')")
    public ResponseEntity<CreateCheckoutResponse> createCheckoutSession(@PathVariable("bookingId") Integer bookingId,
                                                                        @RequestHeader(value = "x-payment-provider", defaultValue = "STRIPE") PaymentProvider paymentProvider) {
        return ResponseEntity.ok(paymentService.createCheckoutSession(bookingId, paymentProvider));
    }
}
