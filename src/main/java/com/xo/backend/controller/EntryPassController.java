package com.xo.backend.controller;

import com.xo.backend.model.ConsumptionResult;
import com.xo.backend.model.dto.EntryPassConsumptionResponseDTO;
import com.xo.backend.model.dto.EntryPassInfoDTO;
import com.xo.backend.service.EntryPassService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/v1/venues/{venueId}/events/{eventId}/entry-passes")
@RequiredArgsConstructor
public class EntryPassController {

    private final EntryPassService entryPassService;


    @GetMapping("/{entryPassId}")//admin
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN')" +
            " OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<EntryPassInfoDTO> getEntryPassInfo(@PathVariable String venueId,
                                                             @PathVariable Integer eventId,
                                                             @PathVariable UUID entryPassId) {

        return ResponseEntity.ok(entryPassService.getEntryPassInfo(venueId, eventId, entryPassId));
    }

    @PutMapping("/{entryPassId}/consume")//admin
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN')" +
            " OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<EntryPassConsumptionResponseDTO> consumeEntryPass(@PathVariable String venueId,
                                                                            @PathVariable Integer eventId,
                                                                            @PathVariable UUID entryPassId) {

        EntryPassConsumptionResponseDTO responseDTO = entryPassService.consumeEntryPass(venueId, eventId, entryPassId);

        if (responseDTO.consumptionResult().equals(ConsumptionResult.FAILURE)) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(responseDTO);
        }
        return ResponseEntity.ok(responseDTO);
    }
}
