package com.xo.backend.controller;

import com.xo.backend.database.service.VenueService;
import com.xo.backend.model.dto.responses.VenueDetailsResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/venues")
@RequiredArgsConstructor
public class VenueController {

    private final VenueService venueService;

    @GetMapping("/{venueId}")
    public ResponseEntity<VenueDetailsResponseDTO> getVenueDetails(@PathVariable String venueId) {
        VenueDetailsResponseDTO response = venueService.getVenueDetails(venueId);
        return ResponseEntity.ok(response);
    }
}