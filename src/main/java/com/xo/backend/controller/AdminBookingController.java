package com.xo.backend.controller;

import com.xo.backend.model.dto.*;
import com.xo.backend.model.dto.request.booking.BookingStatusBulkUpdateRequestDTO;
import com.xo.backend.model.dto.request.booking.BookingStatusUpdateRequestDTO;
import com.xo.backend.model.dto.responses.CreateBookingResponseDTO;
import com.xo.backend.service.AdminBookingService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/v1/venues/{venueId}")
@RequiredArgsConstructor
public class AdminBookingController {

    private final AdminBookingService bookingService;

    @PostMapping("/events/{eventId}/bookings")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<CreateBookingResponseDTO> createAdminBooking(@PathVariable("venueId") String venueId,
                                                                       @PathVariable("eventId") Integer eventId,
                                                                       @RequestBody AdminCreateBookingRequest adminCreateBookingRequest) {

        return ResponseEntity.ok(bookingService.createAdminBooking(venueId, eventId, adminCreateBookingRequest));
    }

    @PatchMapping("/bookings/{bookingId}/status")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<BookingDTO> updateBookingStatus(@PathVariable("venueId") String venueId,
                                                          @PathVariable("bookingId") Integer bookingId,
                                                          @RequestBody BookingStatusUpdateRequestDTO bookingStatusUpdateRequestDTO) {

        return ResponseEntity.ok(bookingService.updateBookingStatus(venueId, bookingId, bookingStatusUpdateRequestDTO));
    }

    @PatchMapping("/bookings/{bookingId}")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<BookingDTO> updateBookingPartially(@PathVariable("venueId") String venueId,
                                                          @PathVariable("bookingId") Integer bookingId,
                                                          @RequestBody AdminUpdateBookingRequest adminUpdateBookingRequest) {

        return ResponseEntity.ok(bookingService.updateBookingPartially(venueId, bookingId, adminUpdateBookingRequest));
    }

    @PatchMapping("/bookings/status")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<List<BookingDTO>> bulkUpdateBookingStatus(@PathVariable("venueId") String venueId,
                                                                    @Valid @RequestBody BookingStatusBulkUpdateRequestDTO bookingStatusUpdateRequestDTO) {

        return ResponseEntity.ok(bookingService.bulkUpdateBookingStatus(venueId, bookingStatusUpdateRequestDTO));
    }

    @GetMapping("/bookings/filter")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_STANDARD')")
    public ResponseEntity<Page<AdminDetailedBookingDTO>> getBookingsByFilter(@PathVariable("venueId") String venueId,
                                                                        @Parameter(description = "Dynamic query parameters to filter bookings. Supported keys: eventName, bookingId, ownerName, ownerId, eventId.",
                                                                                schema = @Schema(type = "object", example = "{\"eventName\": \"Event A\", \"bookingId\": \"123\", \"ownerName\": \"John Doe\", \"bookingNumber\": \"BK2024001\"}"))
                                                                        @RequestParam Map<String, String> queryParams,
                                                                        Pageable pageable) {

        return ResponseEntity.ok(bookingService.getBookingsByFilter(venueId, queryParams, pageable));
    }

    @GetMapping("/users/{userId}/statistics")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasPermission(#venueId, 'STORE', 'TENANT_ADMIN') OR hasPermission(#venueId, 'STORE', 'STORE_ADMIN') OR hasPermission(#venueId, 'VENUE', 'STORE_STANDARD')")
    public ResponseEntity<BookingStatisticsDTO> getBookingStatistics(@PathVariable("venueId") String venueId,@PathVariable("userId") Integer userId) {
        return ResponseEntity.ok(bookingService.getBookingStatistics(venueId,userId));
    }

}
