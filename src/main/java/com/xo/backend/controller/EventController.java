package com.xo.backend.controller;

import com.xo.backend.model.dto.EventBrowseDTO;
import com.xo.backend.model.dto.EventDetailsDTO;
import com.xo.backend.service.EventService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/v1/events")
@RequiredArgsConstructor
@Validated
public class EventController {

    private final EventService eventService;

    @Deprecated
    @GetMapping//user open
    public ResponseEntity<Page<EventBrowseDTO>> getAllPublishedEvents(Pageable pageable) {
        return ResponseEntity.ok(eventService.getUpcomingPublishedEvents(pageable));
    }

    @GetMapping("/{eventId}") //user open
    public ResponseEntity<EventDetailsDTO> getPublishedEventById(@PathVariable("eventId") int eventId) {
        EventDetailsDTO eventDetails = eventService.getPublishedEventById(eventId);
        return ResponseEntity.ok(eventDetails);
    }

}