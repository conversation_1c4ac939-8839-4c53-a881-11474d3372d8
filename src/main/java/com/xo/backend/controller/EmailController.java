package com.xo.backend.controller;

import com.xo.backend.client.email.EmailService;
import com.xo.backend.model.dto.request.SupportEmailRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/emails")
@RequiredArgsConstructor
public class EmailController {

    private final EmailService emailService;

    @PostMapping("/support-email")
    public ResponseEntity<Void> sendSupportEmail(@Valid @RequestBody SupportEmailRequest supportEmailDTO) {

        emailService.sendSupportEmail(supportEmailDTO.subject(), supportEmailDTO.message());

        return ResponseEntity.ok().build();
    }
}
