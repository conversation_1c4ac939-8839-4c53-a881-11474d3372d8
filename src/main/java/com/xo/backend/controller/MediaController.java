package com.xo.backend.controller;

import com.xo.backend.model.UploadFileType;
import com.xo.backend.model.dto.responses.MediaUploadResponseDTO;
import com.xo.backend.service.MediaService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/v1/media")
@RequiredArgsConstructor
public class MediaController {

    private final MediaService mediaService;

    @PostMapping
    public ResponseEntity<MediaUploadResponseDTO> uploadFile(@RequestParam("file") MultipartFile file, @RequestParam("fileType") UploadFileType uploadFileType) {

        MediaUploadResponseDTO response = mediaService.uploadFile(file, uploadFileType);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    public ResponseEntity<InputStreamResource> downloadFile(@RequestParam("fileName") String fileName) {

        return ResponseEntity.ok(mediaService.downloadFile(fileName));
    }

    @DeleteMapping
    public ResponseEntity<String> deleteFile(@RequestParam("fileName") String fileName) {

        mediaService.deleteFile(fileName);
        return ResponseEntity.ok("File deleted successfully");
    }

    @PutMapping
    public ResponseEntity<MediaUploadResponseDTO> replaceFile(@RequestParam("file") MultipartFile file, @RequestParam("fileName") String fileName, @RequestParam("fileType") UploadFileType uploadFileType) {

        MediaUploadResponseDTO response = mediaService.replaceFile(file, fileName, uploadFileType);
        return ResponseEntity.ok(response);
    }

}
