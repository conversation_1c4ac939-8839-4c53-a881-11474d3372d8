package com.xo.backend.model;

import lombok.Getter;

import java.util.List;

@Getter
public enum UploadFileType {
    BANNER(
            List.of("image/jpeg", "image/png"),
            true,
            1080,
            1080,
            20L * 1024),

    REEL(
            List.of("video/quicktime", "video/mp4"),
            false,
            0,
            0,
            0L);


    private final List<String> acceptableFileTypes;
    private final boolean isCompressionRequired;
    private final Integer recommendedImageWidth;
    private final Integer recommendedImageHeight;
    private final Long recommendedSizeInBytes;


    UploadFileType(List<String> acceptableFileTypes, boolean isCompressionRequired, int recommendedImageWidth, int recommendedImageHeight, Long recommendedImageSizeInBytes) {
        this.acceptableFileTypes = acceptableFileTypes;
        this.isCompressionRequired = isCompressionRequired;
        this.recommendedImageWidth = recommendedImageWidth;
        this.recommendedImageHeight = recommendedImageHeight;
        this.recommendedSizeInBytes = recommendedImageSizeInBytes;
    }
}