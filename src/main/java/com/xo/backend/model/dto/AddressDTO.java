package com.xo.backend.model.dto;

import com.xo.backend.controller.groups.AddressValidationGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

@Builder
public record AddressDTO(

        Integer id,
        @NotNull(groups = AddressValidationGroup.class)
        String addressName,
        @NotNull(groups = AddressValidationGroup.class)
        String addressLine1,
        String addressLine2,
        @NotNull(groups = AddressValidationGroup.class)
        String city,
        String state,
        @NotNull(groups = AddressValidationGroup.class)
        String country,
        String zipCode,
        @NotNull(groups = AddressValidationGroup.class)
        Double latitude,
        @NotNull(groups = AddressValidationGroup.class)
        Double longitude
) {
}