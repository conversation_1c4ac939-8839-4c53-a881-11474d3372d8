package com.xo.backend.model.dto;

import com.xo.backend.database.entity.entrypass.EntryPassUsageEnum;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Builder
public record EntryPassDTO(
        UUID entryPassId,
        String bookingTierName,
        String qrCode, // Base64 encoded image
        String venueName,
        String eventName,
        String entryPassReferenceNumber,
        BigDecimal shownPrice,
        String bookedByFullName,
        String bookingNumber,
        OffsetDateTime eventStartTime,
        String entryPassDescription,
        String entryPassIncludedConsumptionAmount,
        String entryPassIncludedConsumptionText,
        EntryPassUsageEnum used
) {
}
