package com.xo.backend.model.dto;

import com.xo.backend.database.entity.events.EventStatus;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.Instant;

@Builder
public record EventVenueDTO(
        Integer id,
        String eventBanner,
        String eventName,
        BigDecimal displayPrice,
        String timeZone,
        Instant startDatetime,
        Instant endDatetime,
        EventStatus status
) {}