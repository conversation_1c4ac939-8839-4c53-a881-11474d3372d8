package com.xo.backend.model.dto.request.event;

import com.xo.backend.controller.groups.AddressValidationGroup;
import com.xo.backend.database.entity.events.EventStatus;
import com.xo.backend.database.entity.events.MinimumAge;
import com.xo.backend.database.entity.events.MusicStyle;
import com.xo.backend.model.dto.AddressDTO;
import com.xo.backend.model.dto.BookingTierDTO;
import com.xo.backend.model.event.EventAttributeLanguageDTO;
import com.xo.backend.model.event.Media;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.groups.ConvertGroup;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;


@Builder
public record CreateEventRequestDTO(
        @NotNull
        OffsetDateTime startDatetime,
        @NotNull
        OffsetDateTime endDatetime,
        Boolean allowPreSaleDiscountedPrices,
        Media media,
        String venueId,
        BigDecimal displayPrice,
        EventAttributeLanguageDTO eventAttributeLanguage,
        OffsetDateTime bookingDeadline,
        String lineup,
        EventStatus status,
        MinimumAge minimumAge,
        List<MusicStyle> musicStyleList,
        List<BookingTierDTO> bookingTiers,
        Boolean useCustomAddress,
        @Valid @ConvertGroup(to = AddressValidationGroup.class)
        AddressDTO customAddress

) {
}
