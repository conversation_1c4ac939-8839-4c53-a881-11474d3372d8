package com.xo.backend.model.dto.responses;

import com.xo.backend.model.dto.EventVenueDTO;
import com.xo.backend.model.dto.go.Address;
import com.xo.backend.model.dto.responses.venueresponse.Coordinates;
import com.xo.backend.model.dto.responses.venueresponse.OperatingTime;
import lombok.Builder;

import java.util.List;
import java.util.Map;

@Builder
public record VenueDetailsResponseDTO(
        Integer id,
        String name,
        String description,
        Address address,
        String phoneNumber,
        List<EventVenueDTO> upcomingEvents,
        List<EventVenueDTO> pastEvents,
        String image,
        String  logo,
        List<OperatingTime> operatingTimes,
        Map<String, String> operatingSchedule,
        Coordinates coordinates,
        Map<String, String> socialLinks
) {
}