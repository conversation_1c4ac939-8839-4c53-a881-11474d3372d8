package com.xo.backend.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.xo.backend.database.entity.bookings.BookingChannel;
import com.xo.backend.database.entity.bookings.BookingOccasion;
import com.xo.backend.database.entity.bookings.BookingStatus;
import lombok.Builder;

import java.util.List;

@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public record BookingDTO(
        Integer bookingId,
        BookingStatus bookingStatus,
        String bookingName,
        String comment,
        String notes,
        BookingOccasion bookingOccasion,
        String ownerEmail,
        String ownerPhone,
        List<BookingItemDTO> bookingItems,
        BookingChannel bookingChannel
) {
}