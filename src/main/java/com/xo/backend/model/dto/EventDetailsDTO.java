package com.xo.backend.model.dto;


import com.xo.backend.database.entity.events.EventStatus;
import com.xo.backend.model.dto.responses.venueresponse.Coordinates;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

@Builder
public record EventDetailsDTO(
        Integer id,
        Integer venueId,
        String title,
        BigDecimal displayPrice,
        String currencyIso,
        String description,
        Boolean allowPreSaleDiscountedPrices,
        OffsetDateTime startDatetime,
        OffsetDateTime endDatetime,
        OffsetDateTime bookingDeadline,
        EventStatus status,
        String banner,
        String reel,
        String minimumAge,
        String lineup,
        Boolean useCustomAddress,
        AddressDTO customAddress,
        String venueName,
        Coordinates coordinates,
        List<String> musicStyleList,
        List<BookingTierDTO> bookingTiers,
        String timeZone
) {
}
