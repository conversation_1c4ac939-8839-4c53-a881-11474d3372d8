package com.xo.backend.model.dto;

import lombok.Builder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Builder
public record EventBrowseDTO(
        Integer id,
        String title,
        String venueName,
        String cityName,
        String countryName,
        OffsetDateTime startDatetime,
        String banner,
        BigDecimal displayPrice,
        String currencyIso,
        String timeZone
) {}