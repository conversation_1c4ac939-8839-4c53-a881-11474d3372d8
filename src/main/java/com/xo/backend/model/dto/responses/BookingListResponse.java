package com.xo.backend.model.dto.responses;

import com.xo.backend.database.entity.bookings.BookingType;
import com.xo.backend.database.entity.events.EventStatus;
import lombok.Builder;

import java.time.OffsetDateTime;

@Builder
public record BookingListResponse(
        Integer bookingId,
        BookingType bookingType,
        String status,
        String bookingName,
        boolean hasEntryPasses,
        Integer eventId,
        EventStatus eventStatus,
        String eventBanner,
        String eventTitle,
        OffsetDateTime eventDateTime,
        String venueName,
        String timeZone
) {
}
