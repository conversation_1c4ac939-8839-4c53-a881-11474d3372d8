package com.xo.backend.model.dto;

import com.xo.backend.model.EventBookingStatisticsPerDate;
import com.xo.backend.model.EventStatistics;
import com.xo.backend.model.EventTierStatistics;
import lombok.Builder;

import java.util.List;


@Builder
public record EventStatisticsDTO(List<EventBookingStatisticsPerDate> eventBookingStatisticsPerDates,
                                 List<EventTierStatistics> eventTierStatistics,
                                 EventStatistics eventStatistics) {
}
