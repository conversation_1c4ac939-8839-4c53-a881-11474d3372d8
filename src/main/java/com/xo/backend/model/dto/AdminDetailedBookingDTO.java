package com.xo.backend.model.dto;

import com.xo.backend.database.entity.bookings.*;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

@Builder
public record AdminDetailedBookingDTO(
        Integer id,
        Integer eventId,
        String eventBanner,
        String eventTitle,
        OffsetDateTime eventDateTime,
        BookingType bookingType,
        Integer numberOfEntryPasses,
        String status,
        List<BookingStatus> validBookingStatusActions,
        BookingConfirmationRequirementEnum requireConfirmation,
        Integer ownerId,
        String ownerName,
        String bookingNumber,
        String bookingName,
        String bookingOccasion,
        String comment,
        String notes,
        BigDecimal netAmount,
        BigDecimal totalFee,
        BigDecimal totalAmount,
        PaymentStatus paymentStatus,
        Integer createdBy,
        String creatorName,
        OffsetDateTime arrivalTime,
        Integer arrivalCounter,
        OffsetDateTime submittedAt,
        BookingChannel bookingChannel,
        List<BookingItemDTO> bookingItems,
        List<EntryPassDTO> entryPasses,
        String timeZone) {
}
