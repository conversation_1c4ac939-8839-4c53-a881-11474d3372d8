package com.xo.backend.model.dto;

import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.model.dto.responses.venueresponse.Coordinates;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

@Builder
public record DetailedBookingDTO(
        Integer id,
        String eventBanner,
        String eventTitle,
        OffsetDateTime eventDateTime,
        String venueName,
        BookingType bookingType,
        Integer numberOfEntryPasses,
        String status,
        List<BookingStatus> validBookingStatusActions,
        BookingConfirmationRequirementEnum requireConfirmation,
        Integer ownerId,
        String ownerName,
        String bookingNumber,
        String bookingName,
        BookingOccasion bookingOccasion,
        String comment,
        BigDecimal netAmount,
        BigDecimal totalFee,
        BigDecimal totalAmount,
        String currencyIso,
        PaymentStatus paymentStatus,
        Integer createdBy,
        String creatorName,
        OffsetDateTime arrivalTime,
        Integer arrivalCounter,
        OffsetDateTime submittedAt,
        BookingChannel bookingChannel,
        Coordinates coordinates,
        List<BookingItemDTO> bookingItems,
        List<EntryPassDTO> entryPasses,
        String timeZone
) {
}