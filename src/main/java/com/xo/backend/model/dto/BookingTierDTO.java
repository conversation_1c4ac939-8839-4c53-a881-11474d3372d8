package com.xo.backend.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.xo.backend.database.entity.bookings.BookingConfirmationRequirementEnum;
import com.xo.backend.database.entity.bookings.BookingTierType;
import lombok.Builder;

import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public record BookingTierDTO(
        Integer id,
        BookingTierType type,
        BigDecimal payOnlinePrice,
        boolean differentPreSalePrice,
        BigDecimal preSaleDiscountedPrice,
        BookingTierPriceType priceType,
        String tierName,
        String description,
        BigDecimal minimumSpent,
        BigDecimal includedConsumptionAmount,
        String includedConsumptionDescription,
        Integer availability,
        Integer eventId,
        Integer minPersons,
        Integer maxPersons,
        BookingConfirmationRequirementEnum requireConfirmation,
        Boolean isVisible
) {
}
