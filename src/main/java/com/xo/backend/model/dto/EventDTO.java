package com.xo.backend.model.dto;

import com.xo.backend.database.entity.events.MusicStyle;
import com.xo.backend.model.event.EventAttributeLanguageDTO;
import com.xo.backend.model.event.Media;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

@Builder
public record EventDTO( //TODO XO-7: Review these fields, potentially refactor?
                        Media media,
                        String venueId,
                        BigDecimal displayPrice,
                        EventAttributeLanguageDTO eventAttributeLanguage,
                        OffsetDateTime bookingDeadline,
                        String lineup,
                        List<MusicStyle> musicStyles,
                        Boolean saveAsDraft,
                        String currency,
                        List<BookingTierDTO> bookingTierDTOS

) {
}