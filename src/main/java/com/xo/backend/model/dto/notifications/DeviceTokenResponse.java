package com.xo.backend.model.dto.notifications;

import lombok.Builder;

import java.time.OffsetDateTime;

@Builder
public record DeviceTokenResponse(
        Integer userId,
        String deviceToken,
        OffsetDateTime tokenExpiry,
        String deviceName,
        String deviceOs,
        OffsetDateTime lastNotificationSent,
        Boolean pushEnabled,
        Boolean isActive,
        OffsetDateTime createdAt,
        OffsetDateTime updatedAt
) {
}
