package com.xo.backend.model.dto;

import com.xo.backend.database.entity.bookings.BookingOccasion;
import com.xo.backend.database.entity.bookings.BookingType;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.UUID;

@Builder
public record EntryPassInfoDTO(
        UUID entryPassId,
        String entryPassReferenceNumber,
        BookingType bookingType,
        String bookingTierName,
        String bookingNumber,
        Integer bookingId,
        String specialRequests,
        BookingOccasion occasion,
        String notes,
        String reservationName,
        Integer reservationsCount,
        BigDecimal payOnlinePrice,
        BigDecimal minimumSpent,
        BigDecimal includedConsumptionAmount,
        String includedConsumptionDescription,
        Integer totalScannedPasses,
        Integer totalBookingPasses,
        boolean isUsed
) {
}