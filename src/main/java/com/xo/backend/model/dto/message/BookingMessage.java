package com.xo.backend.model.dto.message;

import com.xo.backend.database.entity.bookings.BookingChannel;
import com.xo.backend.database.entity.bookings.BookingConfirmationRequirementEnum;
import lombok.Builder;

import java.math.BigDecimal;

@Builder
public record BookingMessage (Integer bookingId,
                              BookingConfirmationRequirementEnum requireConfirmation,
                              BookingChannel bookingChannel,
                              BigDecimal totalAmount){ }
