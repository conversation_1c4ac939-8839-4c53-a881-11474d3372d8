package com.xo.backend.model.dto;

import com.xo.backend.database.entity.bookings.BookingOccasion;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.entity.bookings.PaymentStatus;
import lombok.Builder;

import java.util.List;

@Builder
public record AdminCreateBookingRequest(
        Integer eventId,
        String comment,
        String notes,
        Boolean guestList,
        BookingStatus status,
        BookingOccasion bookingOccasion,
        Integer ownerId,
        String ownerName,
        String ownerEmail,
        String ownerPhone,
        PaymentStatus paymentStatus,
        List<BookingItemDTO> bookingItems
) {
}
