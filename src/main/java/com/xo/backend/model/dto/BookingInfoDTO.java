package com.xo.backend.model.dto;

import com.xo.backend.database.entity.bookings.BookingType;
import lombok.Builder;

import java.time.OffsetDateTime;

@Builder
public record BookingInfoDTO(
        Integer bookingId,
        String eventBanner,
        String eventTitle,
        OffsetDateTime eventDateTime,
        String venueName,
        BookingType bookingType,
        String status,
        String bookingName,
        boolean hasEntryPasses
) {
}
