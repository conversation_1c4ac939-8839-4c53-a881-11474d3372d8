package com.xo.backend.model.dto;

import lombok.Builder;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Builder
public record BookingItemDTO(
        int id,
        int quantity,
        int numOfPeople,
        BookingTierDTO bookingTier,
        int bookingTierId,
        BigDecimal purchasePrice,
        BigDecimal totalAmount,
        List<UUID> entryPassIds
) {
}
