package com.xo.backend.model.dto;

import lombok.Getter;

@Getter
public enum SupportEmailOptions {
    BOOKING_INQUIRY("Booking-related Inquiry"),
    UPDATE_PROFILE("Update Profile"),
    TECHNICAL_ISSUE("Technical Issue"),
    GENERAL_FEEDBACK("General Feedback"),
    PROPOSE_FEATURE("Propose Feature"),
    COMMERCIAL_PARTNERSHIP("Commercial Partnership"),
    OTHER("Other Requests");

    private final String displayName;

    SupportEmailOptions(String displayName) {
        this.displayName = displayName;
    }
}
