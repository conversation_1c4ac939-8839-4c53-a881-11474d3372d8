package com.xo.backend.model.customer;

import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.utlis.Constants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.function.Function;

@Component
public class CustomerCsvMapper implements Function<BookingEntity, CustomerCsvDTO> {
    @Override
    public CustomerCsvDTO apply(BookingEntity booking) {
        int numberOfEntryPasses = 0;
        int numberOfReservations = 0;
        StringBuilder bookingItemDetails = new StringBuilder();

        for (Iterator<BookingItemEntity> iterator = booking.getBookingItems().iterator(); iterator.hasNext(); ) {
            BookingItemEntity bookingItemEntity = iterator.next();
            String title = bookingItemEntity.getBookingTier()
                    .getBookingTierAttributes()
                    .stream()
                    .filter(attr -> Constants.TIER_NAME.equals(attr.getAttribute().getName()))
                    .map(BookingTierAttributeEntity::getAttributeValue)
                    .findFirst()
                    .orElse("");

            bookingItemDetails
                    .append(bookingItemEntity.getQuantity())
                    .append("x ")
                    .append(title);

            if (iterator.hasNext()) {
                bookingItemDetails.append("\r\n");
            }

            BookingTierType tierType = bookingItemEntity.getBookingTier().getType();
            if (tierType == BookingTierType.TICKET) {
                numberOfEntryPasses += bookingItemEntity.getQuantity();
            } else if (tierType == BookingTierType.RESERVATION) {
                numberOfEntryPasses += bookingItemEntity.getNumOfPeople();
                numberOfReservations += bookingItemEntity.getQuantity();
            }
        }

        String reservationNumberDisplay = booking.getBookingType().equals(BookingType.MIXED) ? String.valueOf(numberOfReservations) : "N/A";

        return CustomerCsvDTO.builder()
                .name(booking.getOwnerName())
                .createdBy(StringUtils.join(booking.getBookingChannel() == BookingChannel.ADMIN_DASHBOARD ? "Admin" : "Customer", ": ", booking.getCreatorName()))
                .email(booking.getOwnerEmail())
                .phone(booking.getOwnerPhone())
                .bookingNumber(booking.getBookingNumber())
                .bookingStatus(booking.getStatus())
                .bookingType(booking.getBookingType())
                .numberOfEntryPasses(String.valueOf(numberOfEntryPasses))
                .bookingItemDetails(bookingItemDetails.toString())
                .numberOfReservations(reservationNumberDisplay)
                .paymentStatus(booking.getPaymentStatus() != null ? booking.getPaymentStatus().getDisplayValue() : "-")
                .build();
    }
}
