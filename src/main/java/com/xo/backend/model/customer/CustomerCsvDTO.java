package com.xo.backend.model.customer;

import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.entity.bookings.BookingType;
import lombok.Builder;

@Builder
public class CustomerCsvDTO {
    private String name;
    private String createdBy;
    private String email;
    private String phone;
    private String bookingNumber;
    private BookingStatus bookingStatus;
    private BookingType bookingType;
    private String numberOfEntryPasses;
    private String bookingItemDetails;
    private String numberOfReservations;
    private String paymentStatus;

    public String[] getRowValues() {
        return new String[]{name, createdBy,email, phone, bookingNumber, bookingStatus.name(),bookingType.name(),numberOfEntryPasses,bookingItemDetails,numberOfReservations,paymentStatus};
    }
}
