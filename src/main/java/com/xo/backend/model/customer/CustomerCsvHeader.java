package com.xo.backend.model.customer;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum CustomerCsvHeader {
    NAME("Name"),
    CREATED_BY("Created By"),
    EMAIL("Email"),
    PHONE("Phone"),
    BOOKING_NUMBER("Booking number"),
    BOOKING_STATUS("Booking status"),
    BOOKING_TYPE("Booking type"),
    NUMBER_OF_ENTRY_PASSES("Number of entry passes"),
    BOOKING_ITEM_DETAILS("Booking item details"),
    NUMBER_OF_RESERVATIONS("Number of reservations"),
    PAYMENT_STATUS("Payment status");

    private final String header;

    CustomerCsvHeader(String header) {
        this.header = header;
    }

    public static String[] getAllHeaders() {
        return Arrays.stream(values())
                .map(CustomerCsvHeader::getHeader)
                .toArray(String[]::new);
    }
}
