package com.xo.backend.database.repository;

import com.xo.backend.database.entity.AttributeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AttributeRepository extends JpaRepository<AttributeEntity, Integer> {

    List<AttributeEntity> findAllByNameIn(List<String> attributeNames);
    Optional<AttributeEntity> findByName(String name);
}
