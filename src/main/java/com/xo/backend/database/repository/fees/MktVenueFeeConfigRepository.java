package com.xo.backend.database.repository.fees;

import com.xo.backend.database.entity.fees.MktVenueFeeConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MktVenueFeeConfigRepository extends JpaRepository<MktVenueFeeConfigEntity, Integer> {

    Optional<MktVenueFeeConfigEntity> findByVenueId(String venueId);
}
