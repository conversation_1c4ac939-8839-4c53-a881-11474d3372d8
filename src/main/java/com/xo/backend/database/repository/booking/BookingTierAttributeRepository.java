package com.xo.backend.database.repository.booking;

import com.xo.backend.database.entity.bookings.BookingTierAttributeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import java.util.Optional;

public interface BookingTierAttributeRepository extends JpaRepository<BookingTierAttributeEntity, Integer> {

    Optional<BookingTierAttributeEntity> findByBookingTierIdAndAttributeId(int bookingTierId, int attributeId);
}

