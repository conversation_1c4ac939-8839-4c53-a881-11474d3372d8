package com.xo.backend.database.repository;

import com.xo.backend.database.entity.LanguageEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LanguageRepository extends JpaRepository<LanguageEntity, Integer> {
    List<LanguageEntity> findAllByNameIn(List<String> names); //maybe use optional here
}
