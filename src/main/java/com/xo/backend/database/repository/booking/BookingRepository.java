package com.xo.backend.database.repository.booking;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.model.dto.BookingStatistics;
import jakarta.persistence.Tuple;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookingRepository extends JpaRepository<BookingEntity, Integer>, JpaSpecificationExecutor<BookingEntity> {
    Optional<BookingEntity> findBookingEntityByOwnerIdAndEventId(int ownerId, int eventId);

    boolean existsByEventId(Integer eventId);

    Page<BookingEntity> findBookingEntitiesByOwnerIdAndStatusIn(int ownerId, List<BookingStatus> statuses, Pageable pageable);

    List<BookingEntity> findByOwnerId(Integer ownerId);

    List<BookingEntity> findAllByEventIdAndStatusIn(int eventId,List<BookingStatus> statuses);

    @Query("SELECT b.status as status, COUNT(b) as count FROM BookingEntity b WHERE b.status <> 'DRAFT' AND b.eventId =:eventId GROUP BY b.status")
    List<Tuple> getBookingAttendance(Integer eventId);


    Integer countByEventId(Integer eventId);

    @Modifying
    @Query("UPDATE BookingEntity b SET b.status = 'CANCELLED' WHERE b.event.id = :eventId")
    void markAllAsCancelledForEvent(@Param("eventId") Integer eventId);

    Optional<BookingEntity> findByOwnerIdAndEventIdAndStatus(int ownerId, int eventId, BookingStatus status);


    @Query("SELECT b FROM BookingEntity b WHERE b.ownerId = :userId " +
            "AND b.status IN :status " +
            "AND b.event.endDatetime > :now " +
            "ORDER BY b.event.startDatetime ASC")
    Page<BookingEntity> findUpcomingBookings(@Param("userId") Integer userId,
                                             @Param("now") Instant now,
                                             @Param("status") List<BookingStatus> status,
                                             Pageable pageable);

    @Query("SELECT b FROM BookingEntity b WHERE b.ownerId = :userId " +
            "AND b.status IN :status " +
            "AND b.event.endDatetime <= :now " +
            "ORDER BY b.event.endDatetime DESC")
    Page<BookingEntity> findCompletedBookings(@Param("userId") Integer userId,
                                              @Param("now") Instant now,
                                              @Param("status") List<BookingStatus> status,
                                              Pageable pageable);

    @Query("SELECT count(DISTINCT b.id) as bookingCount," +
            "MIN(b.createdAt) as firstBookingDate, MAX(b.createdAt) as lastBookingDate," +
            "COALESCE(SUM(b.netAmount), 0) as totalSpent, " +
            "COUNT(CASE WHEN b.bookingType = 'TICKET' THEN 1 END) as ticketCount, " +
            "COUNT(CASE WHEN b.bookingType = 'RESERVATION' THEN 1 END) as reservationCount " +
            "from BookingEntity b " +
            "WHERE b.event.venueId=:venueId " +
            "AND b.ownerId=:ownerId " +
            "AND ( b.status = 'CONFIRMED' or b.status = 'ARRIVED')")
    BookingStatistics findBookingStatistics(String venueId, Integer ownerId);

    List<BookingEntity> findAllByBookingTypeNull();

}