package com.xo.backend.database.repository.notifications;

import com.xo.backend.database.entity.notifications.DeviceTokenEntity;
import com.xo.backend.database.entity.notifications.DeviceTokenId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeviceTokenRepository extends JpaRepository<DeviceTokenEntity, DeviceTokenId> {
    List<DeviceTokenEntity> findByUserId(Integer userId);

}