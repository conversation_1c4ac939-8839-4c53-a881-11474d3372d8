package com.xo.backend.database.repository.fees;

import com.xo.backend.database.entity.fees.MarketplaceFeeConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MarketplaceFeeConfigRepository extends JpaRepository<MarketplaceFeeConfigEntity, Integer> {

    Optional<MarketplaceFeeConfigEntity> findByVenueId(String venueId);
}
