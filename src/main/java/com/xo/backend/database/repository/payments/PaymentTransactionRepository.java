package com.xo.backend.database.repository.payments;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.payments.PaymentTransactionEntity;
import com.xo.backend.database.entity.payments.TransactionStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PaymentTransactionRepository extends JpaRepository<PaymentTransactionEntity, String> {
    Optional<PaymentTransactionEntity> findByProviderSessionId(String providerSessionId);
    Optional<PaymentTransactionEntity> findByBookingAndTransactionStatus(BookingEntity bookingEntity, TransactionStatus transactionStatus);
    List<PaymentTransactionEntity> findByBooking_IdAndTransactionStatus(Integer bookingId, TransactionStatus transactionStatus);
    boolean existsByBooking_IdAndTransactionStatus(Integer bookingId, TransactionStatus transactionStatus);
}
