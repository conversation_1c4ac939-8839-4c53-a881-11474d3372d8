package com.xo.backend.database.repository.booking;

import com.xo.backend.database.entity.bookings.BookingTierEntity;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BookingTierRepository extends JpaRepository<BookingTierEntity, Integer> {

    List<BookingTierEntity> findByEventId(Integer eventId);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT bt FROM BookingTierEntity bt WHERE bt.id = :id")
    Optional<BookingTierEntity> findAndLockTierById(Integer id);

    @Query("SELECT SUM(bte.availableQty) FROM BookingTierEntity bte where bte.eventId=:eventId group by bte.eventId")
    Integer getAvailableQuantityByEventId(Integer eventId);

    // In BookingTierRepository
    @Query("SELECT CASE WHEN COUNT(bt) > 0 THEN true ELSE false END FROM BookingTierEntity bt " +
            "WHERE bt.id = :bookingTierId AND bt.event.venueId = :venueId")
    boolean existsByIdAndEventVenueId(Integer bookingTierId, String venueId);
}
