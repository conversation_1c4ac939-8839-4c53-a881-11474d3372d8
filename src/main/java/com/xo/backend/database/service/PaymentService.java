package com.xo.backend.database.service;

import com.xo.backend.client.payments.PaymentAdapter;
import com.xo.backend.client.payments.PaymentProvider;
import com.xo.backend.database.entity.bookings.BookingChannel;
import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.entity.bookings.PaymentStatus;
import com.xo.backend.database.entity.payments.TransactionStatus;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.repository.payments.PaymentTransactionRepository;
import com.xo.backend.error.exceptions.BookingNotFoundException;
import com.xo.backend.error.exceptions.PaymentProviderNotFoundException;
import com.xo.backend.error.exceptions.PaymentRequestException;
import com.xo.backend.error.exceptions.PreSaleDiscountEndedException;
import com.xo.backend.model.dto.responses.payments.CreateCheckoutResponse;
import com.xo.backend.utlis.BookingHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentService {

    private final Map<PaymentProvider, PaymentAdapter> paymentAdapters;
    private final BookingRepository bookingRepository;
    private final PaymentTransactionRepository paymentTransactionRepository;
    private final BookingHelper bookingHelper;

    @Transactional
    public CreateCheckoutResponse createCheckoutSession(Integer bookingId, PaymentProvider paymentProvider) {

        PaymentAdapter adapter = Optional.ofNullable(paymentAdapters.get(paymentProvider)).orElseThrow(() -> new PaymentProviderNotFoundException(paymentProvider.name()));

        BookingEntity bookingEntity = bookingRepository.findById(bookingId).orElseThrow(BookingNotFoundException::new);

        validateBookingStateForPayment(bookingEntity);

        return adapter.createCheckoutSession(bookingEntity);
    }

    @Transactional
    public void expireCheckoutSession(Integer bookingId) {
        paymentTransactionRepository.findByBooking_IdAndTransactionStatus(bookingId, TransactionStatus.PENDING)
                .forEach(paymentTransactionEntity -> paymentAdapters.get(paymentTransactionEntity.getPaymentProvider()).
                        expireCheckoutSession(paymentTransactionEntity));
    }

    private void validateBookingStateForPayment(BookingEntity booking) {
        validateBookingExists(booking);
        validatePaymentStatus(booking);
        validateBookingAmount(booking);
        validateBookingState(booking);
        validatePreSaleDiscount(booking);
    }

    private void validatePreSaleDiscount(BookingEntity booking) {
        if(Boolean.TRUE.equals(booking.getHasPreSaleDiscounts()) && !booking.getEvent().isPreSaleDiscountAvailable()){
            throw new PreSaleDiscountEndedException("Pre sale discount has ended for this event");
        }
    }

    private void validateBookingExists(BookingEntity booking) {
        Objects.requireNonNull(booking, "Booking cannot be null");
        Objects.requireNonNull(booking.getBookingItems(), "Booking items cannot be null");
    }

    private void validatePaymentStatus(BookingEntity booking) {
        if (PaymentStatus.PAID == booking.getPaymentStatus()) {
            throw new PaymentRequestException("Booking already set to paid");
        }
    }

    private void validateBookingAmount(BookingEntity booking) {
        if (booking.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new PaymentRequestException("Invalid booking amount for payment: " + booking.getTotalAmount());
        }
    }

    private void validateBookingState(BookingEntity booking) {
        /*Skip Booking state validation for Admin flow*/
        if (booking.getBookingChannel() == BookingChannel.ADMIN_DASHBOARD) {
            return;
        }
        boolean requiresConfirmation =  bookingHelper.isBookingConfirmationRequired(booking.getRequireConfirmation());

        BookingStatus currentStatus = booking.getStatus();
        if (requiresConfirmation && currentStatus != BookingStatus.APPROVED) {
            throw new PaymentRequestException("Booking requires approval, current status is: " + currentStatus);
        } else if (!requiresConfirmation && currentStatus != BookingStatus.DRAFT) {
            throw new PaymentRequestException("Booking is not in draft state: " + currentStatus);
        }
    }
}
