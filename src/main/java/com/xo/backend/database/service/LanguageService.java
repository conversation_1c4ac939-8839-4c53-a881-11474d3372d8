package com.xo.backend.database.service;

import com.xo.backend.database.entity.LanguageEntity;
import com.xo.backend.database.repository.LanguageRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LanguageService {

    private final LanguageRepository languageRepository;

    public Map<String, LanguageEntity> getLanguageEntityMap(List<String> languageNames) {
        return languageRepository.findAllByNameIn(languageNames)
                .stream()
                .collect(Collectors.toMap(LanguageEntity::getName, l -> l));
    }

    public LanguageEntity getLanguageById(int id) {
        return languageRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Language not found with id: " + id));
    }
}
