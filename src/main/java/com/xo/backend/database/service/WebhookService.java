package com.xo.backend.database.service;

import com.xo.backend.client.payments.stripe.StripeWebhookHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class WebhookService {

    private final StripeWebhookHandler stripeWebhookHandler;

    public void receiveStripeConnectWebhook(String payload, String signature) {
        stripeWebhookHandler.handleStripeConnectWebhook(payload, signature);
    }


}
