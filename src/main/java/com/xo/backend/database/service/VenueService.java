package com.xo.backend.database.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.entity.events.EventStatus;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.mappers.EventDetailsMapper;
import com.xo.backend.model.dto.AddressDTO;
import com.xo.backend.model.dto.EventVenueDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.responses.VenueDetailsResponseDTO;
import com.xo.backend.model.dto.responses.venueresponse.Availability;
import com.xo.backend.model.dto.responses.venueresponse.OperatingTime;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.xo.backend.client.venues.go.ScheduleFormatter.formatVenueSchedules;

@Service
@RequiredArgsConstructor
public class VenueService {

    private final EventRepository eventRepository;
    private final EventDetailsMapper eventMapper;
    private final VenueInfoService venueInfoService;

    @Transactional(readOnly = true)
    public VenueDetailsResponseDTO getVenueDetails(String venueId) {
        VenueDTO venue = venueInfoService.getVenueDetails(venueId);

        Instant now = Instant.now();
        List<EventVenueDTO> pastEvents = eventRepository
                .findPastEventsByVenueIdAndEndDatetimeBeforeAndStatus(venueId, now, EventStatus.PUBLISHED)
                .stream()
                .map((EventEntity event) -> eventMapper.toEventVenueDTO(event,venue))
                .toList();

        List<EventVenueDTO> upcomingEvents = eventRepository
                .findUpcomingEventsByVenueIdAndStartDatetimeAfterAndStatus(venueId, now, EventStatus.PUBLISHED)
                .stream()
                .map((EventEntity event) -> eventMapper.toEventVenueDTO(event,venue))
                .toList();

        Map<String, String> socialLinks = extractSocialLinks(venue);
        List<OperatingTime> operatingTimes = extractOperatingTimes(venue);

        return VenueDetailsResponseDTO.builder()
                .id(venue.id())
                .name(venue.name())
                .logo(venue.settings().get("LOGO"))
                .image(venue.settings().get("IMAGE"))
                .socialLinks(socialLinks)
                .description(venue.description())
                .address(venue.address())
                .phoneNumber(venue.phoneNumber())
                .upcomingEvents(upcomingEvents)
                .pastEvents(pastEvents)
                .coordinates(venue.coordinates())
                .operatingTimes(operatingTimes)
                .operatingSchedule(formatVenueSchedules(venue.specialSchedules()))
                .build();
    }

    private Map<String, String> extractSocialLinks(VenueDTO venue) {
        Map<String, String> socialLinks = new HashMap<>();
        socialLinks.put("facebook", venue.settings().get("FACEBOOK_URL"));
        socialLinks.put("website", venue.settings().get("STORE_WEBSITE_URL"));
        socialLinks.put("instagram", venue.settings().get("INSTAGRAM_URL"));
        socialLinks.put("twitterX", venue.settings().get("TWITTERX_URL"));
        socialLinks.put("tiktok", venue.settings().get("TIKTOK_URL"));
        socialLinks.put("tripAdvisor", venue.settings().get("TRIP_ADVISOR_URL"));
        socialLinks.put("googleBusinessPage", venue.settings().get("GOOGLE_BUSINESS_PAGE_URL"));

        return socialLinks;
    }

    private List<OperatingTime> extractOperatingTimes(VenueDTO venue) {
        if(venue.specialSchedules() == null) {
            return List.of();
        }
        return venue.specialSchedules().stream()
                .map(schedule -> {
                    List<Availability> availabilities = schedule.schedule().availabilities().stream()
                            .map(availability -> new Availability(
                                    availability.startTime(),
                                    availability.endTime(),
                                    availability.daysOfWeek())).toList();

                    return OperatingTime.builder()
                            .type(schedule.type())
                            .availabilities(availabilities).build();
                }).toList();
    }

    public AddressDTO getVenueAddressDetails(EventEntity event, VenueDTO venue) {
        if(Boolean.TRUE.equals(event.getUseCustomAddress())) {
            return AddressDTO.builder()
                    .latitude(event.getCustomAddress().getLatitude())
                    .longitude(event.getCustomAddress().getLongitude())
                    .addressName(event.getCustomAddress().getAddressName())
                    .build();
        }else{
            return AddressDTO.builder().longitude(venue.coordinates().longitude()).latitude(venue.coordinates().latitude()).addressName(venue.name()).build();
        }
    }
}