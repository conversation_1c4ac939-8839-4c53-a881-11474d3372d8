package com.xo.backend.database.service;

import com.xo.backend.database.entity.AttributeEntity;
import com.xo.backend.database.entity.LanguageEntity;
import com.xo.backend.database.entity.bookings.BookingTierAttributeEntity;
import com.xo.backend.database.entity.bookings.BookingTierEntity;
import com.xo.backend.model.dto.BookingTierDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.xo.backend.utlis.Constants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class BookingTierAttributeService {


    private final AttributeService attributeService;

    public List<BookingTierAttributeEntity> createBookingTierAttributeList(BookingTierEntity newBookingTier, BookingTierDTO tier,
                                                                           Map<String, AttributeEntity> attributeMap, Map<String, LanguageEntity> languageMap) {

        List<BookingTierAttributeEntity> bookingTierAttributeEntityList = new ArrayList<>();

        LanguageEntity en = languageMap.get(ENGLISH_LANG);
        addBookingTierAttribute(bookingTierAttributeEntityList, attributeMap, TIER_NAME, tier.tierName(), en, newBookingTier);
        addBookingTierAttribute(bookingTierAttributeEntityList, attributeMap, DESCRIPTION, tier.description(), en, newBookingTier);

        LanguageEntity none = languageMap.get(NO_LANG);
        addBookingTierAttribute(bookingTierAttributeEntityList, attributeMap, MINIMUM_SPENT, getBigDecimalValue(tier.minimumSpent()), none, newBookingTier);
        addBookingTierAttribute(bookingTierAttributeEntityList, attributeMap, INCLUDED_CONSUMPTION_AMOUNT, getBigDecimalValue(tier.includedConsumptionAmount()), none, newBookingTier);
        addBookingTierAttribute(bookingTierAttributeEntityList, attributeMap, INCLUDED_CONSUMPTION_DESCRIPTION, tier.includedConsumptionDescription(), none, newBookingTier);

        return bookingTierAttributeEntityList;
    }

    private static String getBigDecimalValue(BigDecimal amount) {
        return amount == null ? BigDecimal.ZERO.toString() : amount.toString();
    }

    private void addBookingTierAttribute(List<BookingTierAttributeEntity> bookingTierAttributeEntityList, Map<String, AttributeEntity> attributeMap,
                                         String attributeName, String value, LanguageEntity language, BookingTierEntity bookingTier) {
        if (value == null) return;
        AttributeEntity attribute = attributeService.getAttributeByName(attributeMap, attributeName);
        bookingTierAttributeEntityList.add(
                BookingTierAttributeEntity.builder()
                        .bookingTier(bookingTier)
                        .attribute(attribute)
                        .attributeValue(value)
                        .language(language)
                        .build()
        );
    }

    public BookingTierAttributeEntity getBookingTierAttributeByName(List<BookingTierAttributeEntity> bookingTierAttributeList, String name) {
        return bookingTierAttributeList.stream()
                .filter(bookingTierAttribute -> bookingTierAttribute.getAttribute().getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    private static <T> void setBookingTierAttributeIfNotNull(T value, BookingTierAttributeEntity entity) {
        Optional.ofNullable(value)
                .map(Object::toString)
                .ifPresent(entity::setAttributeValue);
    }

    public void updateBookingTierAttributeList(BookingTierEntity bookingTierEntity, BookingTierDTO bookingTierDTO) {
        bookingTierEntity.getBookingTierAttributes().forEach(attr -> {
            switch (attr.getAttribute().getName()) {
                case TIER_NAME -> setBookingTierAttributeIfNotNull(bookingTierDTO.tierName(), attr);

                case DESCRIPTION -> setBookingTierAttributeIfNotNull(bookingTierDTO.description(), attr);

                case MINIMUM_SPENT -> setBookingTierAttributeIfNotNull(bookingTierDTO.minimumSpent(), attr);

                case INCLUDED_CONSUMPTION_AMOUNT ->
                        setBookingTierAttributeIfNotNull(bookingTierDTO.includedConsumptionAmount(), attr);

                case INCLUDED_CONSUMPTION_DESCRIPTION ->
                        setBookingTierAttributeIfNotNull(bookingTierDTO.includedConsumptionDescription(), attr);

                default -> log.warn("Invalid attribute name: {}", attr.getAttribute().getName());
            }
        });
    }
}
