package com.xo.backend.database.service;

import com.xo.backend.database.entity.AttributeEntity;
import com.xo.backend.database.repository.AttributeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AttributeService {

    private final AttributeRepository attributeRepository;

    public Map<String, AttributeEntity> getAttributeEntityMap(List<String> attributeNames) {

        return attributeRepository.findAllByNameIn(attributeNames)
                .stream()
                .collect(Collectors.toMap(AttributeEntity::getName, a -> a));
    }

    public AttributeEntity getAttributeByName(Map<String, AttributeEntity> attributeMap, String name) {
        return Optional.ofNullable(attributeMap.get(name))
                .orElseThrow(() -> new IllegalArgumentException("AttributeEntity not found: " + name));
    }
}
