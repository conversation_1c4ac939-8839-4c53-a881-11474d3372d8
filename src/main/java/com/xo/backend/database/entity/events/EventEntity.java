package com.xo.backend.database.entity.events;

import com.xo.backend.database.entity.address.AddressEntity;
import com.xo.backend.database.entity.audit.DateAudit;
import com.xo.backend.database.entity.bookings.BookingTierEntity;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Entity
@Table(name = "event", schema = "xo")
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper=false)
@Builder
@ToString(exclude = {"eventAttributes", "bookingTiers"})
public class EventEntity extends DateAudit {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "venue_id")
    private String venueId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private EventStatus status;

    @Column(name = "display_price")
    private BigDecimal displayPrice;

    @Column(name = "start_at")
    private Instant startDatetime;

    @Column(name = "end_at")
    private Instant endDatetime;

    @Column(name = "booking_deadline")
    private Instant bookingDeadline;

    @Column(name = "created_by")
    private Integer createdBy;

    @Column(name = "allow_pre_sale_discounted_prices")
    private Boolean allowPreSaleDiscountedPrices;

    @OneToMany(mappedBy = "event", cascade = CascadeType.ALL)
    private List<EventAttributeEntity> eventAttributes;

    @OneToMany(mappedBy = "event", cascade = CascadeType.ALL)
    private List<BookingTierEntity> bookingTiers;

    @ManyToOne
    @JoinColumn(name = "custom_address_id", referencedColumnName = "id")
    private AddressEntity customAddress;

    @Column(name = "use_custom_address")
    private Boolean useCustomAddress;

    public boolean isPreSaleDiscountAvailable() {
        if (!Boolean.TRUE.equals(allowPreSaleDiscountedPrices)) {
            return false;
        }
        Instant now = Instant.now();
        return (bookingDeadline != null ? bookingDeadline : startDatetime).isAfter(now);
    }
}
