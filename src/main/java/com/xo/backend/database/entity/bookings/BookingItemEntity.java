package com.xo.backend.database.entity.bookings;

import com.xo.backend.database.entity.audit.DateAudit;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;


@Entity
@Table(name = "booking_item", schema = "xo")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper=false)
@Data
@Builder
public class BookingItemEntity extends DateAudit {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "quantity")
    private int quantity;

    @Column(name = "num_of_people")
    private Integer numOfPeople;

    @Column(name = "purchase_price")
    private BigDecimal purchasePrice;

    @Column(name = "total_price")
    private BigDecimal totalPrice;

    //foreign keys
    @Column(name = "booking_tier_id",insertable = false,updatable = false)
    private int bookingTierId;

    @ManyToOne
    @JoinColumn(name = "booking_tier_id")
    private BookingTierEntity bookingTier;

    @Column(name = "booking_id")
    private int bookingId;

}
