package com.xo.backend.database.entity.notifications;

import com.xo.backend.database.entity.audit.DateAudit;
import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "device_token", schema = "xo")
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper=false)
@IdClass(DeviceTokenId.class)
@Builder
public class DeviceTokenEntity extends DateAudit {

    @Id
    @Column(name = "user_id", nullable = false)
    private Integer userId;

    @Id
    @Column(name = "device_token", nullable = false)
    private String deviceToken;

    @Column(name = "token_expiry")
    private Instant tokenExpiry;

    @Column(name = "device_name")
    private String deviceName;

    @Column(name = "device_os")
    private String deviceOs;

    @Column(name = "last_notification_sent")
    private Instant lastNotificationSent;

    @Column(name = "push_enabled", nullable = false)
    private Boolean pushEnabled;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive;
}
