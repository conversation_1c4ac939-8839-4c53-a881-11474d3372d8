package com.xo.backend.database.entity.payments;


import com.xo.backend.client.payments.PaymentProvider;
import com.xo.backend.client.payments.SupportedCurrency;
import com.xo.backend.database.entity.audit.DateAudit;
import com.xo.backend.database.entity.bookings.BookingEntity;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "payment_transaction", schema = "xo")
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
public class PaymentTransactionEntity extends DateAudit {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "payment_provider")
    @Enumerated(EnumType.STRING)
    private PaymentProvider paymentProvider;

    @Column(name = "connected_account_id")
    private String connectedAccountId;

    @Column(name = "currency")
    @Enumerated(EnumType.STRING)
    private SupportedCurrency currency;

    @Column(name = "provider_session_id")
    private String providerSessionId;

    @Column(name = "provider_transaction_id")
    private String providerTransactionId;

    @Column(name = "platform_fee")
    private BigDecimal platformFee;

    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    @Column(name = "transaction_status")
    @Enumerated(EnumType.STRING)
    private TransactionStatus transactionStatus;

    @Column(name = "failure_reason")
    private String failureReason;

    @Column(name = "completed_at")
    private Instant completedAt;
    //foreign keys
    @Column(name = "booking_id", updatable = false, insertable = false)
    private Integer bookingId;

    @ManyToOne
    @JoinColumn(name = "booking_id", referencedColumnName = "id")
    private BookingEntity booking;
}
