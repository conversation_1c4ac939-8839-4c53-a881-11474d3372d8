package com.xo.backend.database.entity.payments;

import com.xo.backend.client.payments.PaymentProvider;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;

import java.time.Instant;

@Entity
@Table(name = "webhook_log", schema = "xo")
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class WebhookLogEntity {

    @Id
    @Column(name = "id")
    private String id;

    @Enumerated(EnumType.STRING)
    @Column(name = "provider")
    private PaymentProvider provider;

    @Column(name = "event_type")
    private String eventType;

    @CreatedDate
    @Column(name = "received_at")
    private Instant receivedAt;

}
