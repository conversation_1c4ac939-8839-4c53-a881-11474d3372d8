package com.xo.backend.database.entity.bookings;

import com.xo.backend.database.entity.audit.DateAudit;
import com.xo.backend.database.entity.events.EventEntity;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.annotation.CreatedBy;

import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "booking_tier", schema = "xo")
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@ToString(exclude = {"bookingTierAttributes"})
public class BookingTierEntity extends DateAudit {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private BookingTierType type;

    @Column(name = "pay_online_price")
    private BigDecimal payOnlinePrice;

    @Column(name = "pre_sale_discounted_price")
    private BigDecimal preSaleDiscountedPrice;

    @Column(name = "available_qty")
    private Integer availableQty;

    @Column(name = "min_persons")
    private Integer minPersons;

    @Column(name = "max_persons")
    private Integer maxPersons;

    @Enumerated(EnumType.STRING)
    @Column(name = "require_confirmation")
    private BookingConfirmationRequirementEnum requireConfirmation;

    @Column(name = "is_visible")
    private Boolean isVisible;

    @CreatedBy
    @Column(name = "created_by")
    private Integer createdBy;

    //foreign keys
    @Column(name = "event_id", insertable = false, updatable = false)
    private Integer eventId;

    @ManyToOne
    @JoinColumn(name = "event_id", referencedColumnName = "id")
    private EventEntity event;

    @OneToMany(mappedBy = "bookingTier", cascade = CascadeType.ALL)
    private List<BookingTierAttributeEntity> bookingTierAttributes;
}
