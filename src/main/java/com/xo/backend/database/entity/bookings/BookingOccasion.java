package com.xo.backend.database.entity.bookings;

import lombok.Getter;

@Getter
public enum BookingOccasion {
    BIRTHDAY("Birthday"),
    ANNIVERSARY("Anniversary"),
    ENGAGEMENT("Engagement"),
    BACHELOR("Bachelor Party"),
    HENS_PARTY("Hens Party"),
    GRADUATION("Graduation"),
    PROMOTION("Promotion");

    private final String displayValue;

    BookingOccasion(String displayValue) {
        this.displayValue = displayValue;
    }
}
