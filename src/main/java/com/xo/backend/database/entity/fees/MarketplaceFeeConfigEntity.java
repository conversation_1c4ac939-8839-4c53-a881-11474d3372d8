package com.xo.backend.database.entity.fees;

import com.xo.backend.database.entity.audit.DateAudit;
import com.xo.marketplace.database.MarketPlaceVenueConfig;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Table(name = "marketplace_venue_fee_config", schema = "xo")
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
public class MarketplaceFeeConfigEntity extends DateAudit {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "venue_id", nullable = false, unique = true)
    private String venueId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "venue_id", referencedColumnName = "venue_id", insertable = false, updatable = false)
    private MarketPlaceVenueConfig marketPlaceVenueConfig;

    @Column(name = "app_fee_perc", nullable = false, precision = 5, scale = 4)
    private BigDecimal appFeePerc;

    @Column(name = "app_fee_fixed_per_transaction", nullable = false, precision = 19, scale = 2)
    private BigDecimal appFeeFixedPerTransaction;

    @Column(name = "app_fee_fixed_per_entry_pass", nullable = false, precision = 19, scale = 2)
    private BigDecimal appFeeFixedPerEntryPass;

    @Column(name = "app_fee_min", precision = 19, scale = 2)
    private BigDecimal appFeeMin;

    @Column(name = "app_fee_max", precision = 19, scale = 2)
    private BigDecimal appFeeMax;

    @Column(name = "app_fee_to_customer_perc", nullable = false, precision = 5, scale = 4)
    private BigDecimal appFeeToCustomerPerc;

    @Column(name = "psp_fee_perc", nullable = false, precision = 5, scale = 4)
    private BigDecimal pspFeePerc;

    @Column(name = "psp_fee_fixed", nullable = false, precision = 19, scale = 2)
    private BigDecimal pspFeeFixed;

    @Column(name = "pass_psp_fees", nullable = false)
    private Boolean passPspFees;
}
