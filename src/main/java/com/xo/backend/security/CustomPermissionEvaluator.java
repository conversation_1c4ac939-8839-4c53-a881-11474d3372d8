package com.xo.backend.security;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.error.exceptions.UnauthorizedAccessException;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class CustomPermissionEvaluator implements PermissionEvaluator {
    public static final String STORE = "STORE";
    public static final String EVENT = "EVENT";
    public static final String BOOKING = "BOOKING";
    public static final String ENTRY_PASS = "ENTRY_PASS";

    private final EventRepository eventRepository;

    @Override
    public boolean hasPermission(Authentication auth, Object targetDomainObject, Object permission) {
        if ((auth == null) || (auth instanceof AnonymousAuthenticationToken)) {
            throw new UnauthorizedAccessException("Unauthorized access");
        } else if ((targetDomainObject == null) || !(permission instanceof String)) {
            return false;
        }
        return switch (targetDomainObject) {
            case EventEntity eventEntity ->
                    hasPrivilege(STORE, auth, eventEntity.getVenueId(), permission.toString().toUpperCase());
            case BookingEntity bookingEntity ->
                    hasPrivilege(BOOKING, auth, bookingEntity.getId(), permission.toString().toUpperCase());
            case EntryPassEntity entryPassEntity ->
                    hasPrivilege(ENTRY_PASS, auth, entryPassEntity.getId(), permission.toString().toUpperCase());
            default -> false;
        };
    }

    @Transactional
    @Override
    public boolean hasPermission(
            Authentication auth, Serializable targetId, String targetType, Object permission) {
        if ((auth == null) || (auth instanceof AnonymousAuthenticationToken)) {
            throw new UnauthorizedAccessException("Unauthorized access");
        } else if ((targetType == null) || !(permission instanceof String)) {
            return false;
        }
        return switch (targetType) {
            case EVENT -> eventRepository.findById((Integer) targetId).map(eventEntity -> hasPrivilege(STORE,
                    auth, eventEntity.getVenueId(), permission.toString().toUpperCase())).orElse(false);

            case BOOKING -> hasPrivilege(BOOKING, auth, targetId, permission.toString().toUpperCase());

            case ENTRY_PASS -> hasPrivilege(ENTRY_PASS, auth, targetId, permission.toString().toUpperCase());

            default -> hasPrivilege(STORE,
                    auth, targetId, permission.toString().toUpperCase());
        };
    }

    private boolean hasPrivilege(String resource,
                                 Authentication auth, Serializable targetId, String permission) {

        String checkPermission = resource + "_" + targetId + "_" + permission;
        for (GrantedAuthority grantedAuth : auth.getAuthorities()) {
            if (grantedAuth.getAuthority().equals(checkPermission)) {
                return true;
            }
        }
        return false;
    }
}
