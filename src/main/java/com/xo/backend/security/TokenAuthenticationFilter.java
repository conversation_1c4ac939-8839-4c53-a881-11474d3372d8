package com.xo.backend.security;

import com.xo.backend.client.user.UserInfoService;
import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.repository.entry_pass.EntryPassRepository;
import com.xo.backend.model.dto.UserPermissionsResponseDTO;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


@Component
@RequiredArgsConstructor
@Slf4j
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    public static final String OWNER = "OWNER";
    private final BookingRepository bookingRepository;
    private final EntryPassRepository entryPassRepository;
    private final UserInfoService userInfoService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            try {
                UserPermissionsResponseDTO userPermissionsResponseDTO = userInfoService.getUserPermissions(token);
                Collection<GrantedAuthority> grantedAuthorities = getAuthoritiesFromResponse(userPermissionsResponseDTO);
                grantedAuthorities.addAll(getBookingAuthorities(userPermissionsResponseDTO.id()));
                grantedAuthorities.addAll(getEntryPassAuthorities(userPermissionsResponseDTO.id()));
                TokenAuthentication tokenAuthentication = new TokenAuthentication(userPermissionsResponseDTO.id(),
                        userPermissionsResponseDTO.username(), token.substring(7),grantedAuthorities);
                SecurityContextHolder.getContext().setAuthentication(tokenAuthentication);
            } catch (Exception exception) {
                log.error("Unable to request user permissions", exception);
            }
        }

        filterChain.doFilter(request, response);
    }

    private Collection<GrantedAuthority> getAuthoritiesFromResponse(UserPermissionsResponseDTO userPermissionsResponseDTO) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        if (userPermissionsResponseDTO.superAdmin()) {
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
        }
        if (userPermissionsResponseDTO.platformAdmin()) {
            authorities.add(new SimpleGrantedAuthority("ROLE_PLATFORM_ADMIN"));
        }
        if(userPermissionsResponseDTO.tenantAdmin()){
            authorities.add(new SimpleGrantedAuthority("ROLE_TENANT_ADMIN"));
        }
        if (MapUtils.isNotEmpty(userPermissionsResponseDTO.storeRoles())) {
            for (String storeId : userPermissionsResponseDTO.storeRoles().keySet()) {
                authorities.add(new SimpleGrantedAuthority("STORE" + "_" + storeId + "_" + userPermissionsResponseDTO.storeRoles().get(storeId)));
            }
        }
        return authorities;
    }

    private Collection<GrantedAuthority> getBookingAuthorities(Integer ownerId) {
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        List<BookingEntity> bookingEntities=bookingRepository.findByOwnerId(ownerId);
        for(BookingEntity bookingEntity:bookingEntities) {
            grantedAuthorities.add(new SimpleGrantedAuthority("BOOKING_" + bookingEntity.getId()+"_"+ OWNER));
        }
        return grantedAuthorities;
    }
    private Collection<GrantedAuthority> getEntryPassAuthorities(Integer ownerId) {
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        List<EntryPassEntity> entryPassEntities=entryPassRepository.findByOwnerId(ownerId);
        for(EntryPassEntity entryPassEntity:entryPassEntities) {
            grantedAuthorities.add(new SimpleGrantedAuthority("ENTRY_PASS_" + entryPassEntity.getId()+"_"+ OWNER));
        }
        return grantedAuthorities;
    }
}
