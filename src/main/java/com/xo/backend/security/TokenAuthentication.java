package com.xo.backend.security;

import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

public class TokenAuthentication extends AbstractAuthenticationToken {

    private final Integer principal;
    private final String token;
    @Getter
    private final String username;

    public TokenAuthentication(Integer userId,String username, String token,
                               Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.token = token;
        this.principal = userId;
        this.username = username;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return token;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }

}
