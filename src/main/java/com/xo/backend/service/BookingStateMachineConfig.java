package com.xo.backend.service;

import com.xo.backend.database.entity.bookings.BookingConfirmationRequirementEnum;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.error.exceptions.BookingNotFoundException;
import com.xo.backend.model.dto.message.BookingMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.guard.Guard;
import org.springframework.statemachine.recipes.persist.FactoryPersistStateMachineHandler;

import java.math.BigDecimal;
import java.util.EnumSet;

import static com.xo.backend.database.entity.bookings.BookingChannel.ADMIN_DASHBOARD;
import static com.xo.backend.database.entity.bookings.BookingChannel.USER_APP;


@Configuration
@EnableStateMachineFactory
@RequiredArgsConstructor
@Slf4j
public class BookingStateMachineConfig extends EnumStateMachineConfigurerAdapter<BookingStatus, BookingStatus> {

    public static final String BOOKING_ID = "bookingId";
    public static final String BOOKING_MESSAGE = "bookingMessage";
    private final BookingStateChangeService bookingStateChangeService;


    @Override
    public void configure(StateMachineStateConfigurer<BookingStatus, BookingStatus> states) throws Exception {
        states.withStates()
                .initial(BookingStatus.PENDING)
                .states(EnumSet.allOf(BookingStatus.class));
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<BookingStatus, BookingStatus> transitions) throws Exception {
        transitions
                .withExternal()
                .source(BookingStatus.PENDING)
                .target(BookingStatus.APPROVED)
                .event(BookingStatus.APPROVED)
                .guard(orGuard(adminChannelGuard(), andGuard(andGuard(userChannelGuard(),confirmationRequiredGuard()), priceNotZeroGuard())))
                .and()

                .withExternal()
                .source(BookingStatus.PENDING)
                .target(BookingStatus.CONFIRMED)
                .event(BookingStatus.CONFIRMED)
                .guard(orGuard(adminChannelGuard(),andGuard(userChannelGuard(),confirmationRequiredGuard())))
                .and()

                .withExternal()
                .source(BookingStatus.PENDING)
                .target(BookingStatus.CANCELLED)
                .event(BookingStatus.CANCELLED)
                .guard(orGuard(adminChannelGuard(),andGuard(userChannelGuard(),confirmationRequiredGuard())))
                .and()

                .withExternal()
                .source(BookingStatus.PENDING)
                .target(BookingStatus.DECLINED)
                .event(BookingStatus.DECLINED)
                .guard(orGuard(adminChannelGuard(), andGuard(userChannelGuard(),confirmationRequiredGuard())))
                .and()

                .withExternal()
                .source(BookingStatus.APPROVED)
                .target(BookingStatus.CONFIRMED)
                .event(BookingStatus.CONFIRMED)
                .guard(orGuard(adminChannelGuard(),andGuard(userChannelGuard(),confirmationRequiredGuard())))
                .and()

                .withExternal()
                .source(BookingStatus.APPROVED)
                .target(BookingStatus.CANCELLED)
                .event(BookingStatus.CANCELLED)
                .guard(orGuard(adminChannelGuard(),andGuard(userChannelGuard(),confirmationRequiredGuard())))
                .and()

                .withExternal()
                .source(BookingStatus.APPROVED)
                .target(BookingStatus.DECLINED)
                .event(BookingStatus.DECLINED)
                .guard(orGuard(adminChannelGuard(),andGuard(userChannelGuard(),confirmationRequiredGuard())))
                .and()

                .withExternal()
                .source(BookingStatus.CONFIRMED)
                .target(BookingStatus.CANCELLED)
                .event(BookingStatus.CANCELLED);


    }

    @Bean
    public FactoryPersistStateMachineHandler<BookingStatus, BookingStatus> factoryPersistStateMachineHandler(
            StateMachineFactory<BookingStatus, BookingStatus> stateMachineFactory) {

        FactoryPersistStateMachineHandler<BookingStatus, BookingStatus> handler =
                new FactoryPersistStateMachineHandler<>(stateMachineFactory);

        handler.addPersistStateChangeListener((state, message, transition, stateMachine) -> {
            if (message == null) return;

            BookingMessage bookingMessage = message.getHeaders().get(BOOKING_MESSAGE, BookingMessage.class);
            if (bookingMessage == null) {
                throw new BookingNotFoundException();
            }
            BookingStatus oldStatus = transition.getSource().getId();
            BookingStatus newStatus = state.getId();
            log.info("Updating booking with id {} , from State {} , to State {}", bookingMessage.bookingId(), oldStatus, newStatus);
            bookingStateChangeService.handleStateChange(bookingMessage.bookingId(), oldStatus, newStatus);
            stateMachine.stopReactively().block();
        });

        return handler;
    }

    private  Guard<BookingStatus, BookingStatus> confirmationRequiredGuard() {
        return stateContext -> {
            BookingMessage message = stateContext.getMessage().getHeaders().get(BOOKING_MESSAGE, BookingMessage.class);
            return message != null && BookingConfirmationRequirementEnum.YES == message.requireConfirmation();
        };
    }

    private Guard<BookingStatus, BookingStatus> priceNotZeroGuard() {
        return stateContext -> {
            BookingMessage message = stateContext.getMessage().getHeaders().get(BOOKING_MESSAGE, BookingMessage.class);
            return message != null && BigDecimal.ZERO.compareTo(message.totalAmount()) != 0;
        };
    }

    private  Guard<BookingStatus, BookingStatus> userChannelGuard() {
        return stateContext -> {
            BookingMessage message = stateContext.getMessage().getHeaders().get(BOOKING_MESSAGE, BookingMessage.class);
            return message != null && USER_APP==message.bookingChannel();
        };
    }

    private  Guard<BookingStatus, BookingStatus> adminChannelGuard() {
        return stateContext -> {
            BookingMessage message = stateContext.getMessage().getHeaders().get(BOOKING_MESSAGE, BookingMessage.class);
            return message != null && ADMIN_DASHBOARD==message.bookingChannel();
        };
    }

    private Guard<BookingStatus,BookingStatus> andGuard(Guard<BookingStatus,BookingStatus> guard1, Guard<BookingStatus,BookingStatus> guard2) {
        return stateContext -> guard1.evaluate(stateContext) && guard2.evaluate(stateContext);
    }

    private Guard<BookingStatus,BookingStatus> orGuard(Guard<BookingStatus,BookingStatus> guard1, Guard<BookingStatus,BookingStatus> guard2){
        return stateContext -> guard1.evaluate(stateContext) || guard2.evaluate(stateContext);
    }


}
