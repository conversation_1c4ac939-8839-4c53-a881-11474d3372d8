package com.xo.backend.service;

import com.xo.backend.error.exceptions.CsvExportException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class WorkbookService {

    public byte[] createSingleSheetXlsxWorkbook(String sheetName, String[] headers, List<String[]> data) {
        try (SXSSFWorkbook workbook = new SXSSFWorkbook(100);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            Sheet sheet = workbook.createSheet(sheetName);

            // Keep track of column widths
            int[] maxColumnWidths = new int[headers.length];

            // Initialize with header widths
            for (int i = 0; i < headers.length; i++) {
                maxColumnWidths[i] = headers[i].length();
            }

            int startRow = 0;

            // Create a bold font for headers
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);

            // Create a cell style that uses the bold font
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFont(headerFont);

            // Create header row
            Row headerRow = sheet.createRow(startRow);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Fill data rows
            int rowNum = startRow + 1;
            for (String[] rowData : data) {
                Row row = sheet.createRow(rowNum++);
                for (int i = 0; i < rowData.length && i < headers.length; i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(rowData[i]);

                    // Update maximum width if necessary
                    if (rowData[i] != null && rowData[i].length() > maxColumnWidths[i]) {
                        maxColumnWidths[i] = rowData[i].length();
                    }
                }
            }

            // Set column widths
            for (int i = 0; i < headers.length; i++) {
                // Cap the width at a reasonable maximum (e.g., 50 characters)
                int cappedWidth = Math.min(maxColumnWidths[i] + 2, 50);

                // Convert character count to column width
                // The factor 256 is used because the width is set in units of 1/256th of a character width
                // The additional padding (usually 2-4 characters) provides some margin
                sheet.setColumnWidth(i, cappedWidth * 256);
            }

            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("Failed to generate Excel file", e);
            throw new CsvExportException("Failed to generate Excel file: " + e.getMessage());
        }
    }
}
