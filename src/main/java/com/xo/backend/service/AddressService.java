package com.xo.backend.service;


import com.xo.backend.database.entity.address.AddressEntity;
import com.xo.backend.database.repository.address.AddressRepository;
import com.xo.backend.mappers.AddressMapper;
import com.xo.backend.model.dto.AddressDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class AddressService {

    private final AddressRepository addressRepository;

    private final AddressMapper addressMapper;

    public AddressEntity createOrUpdateAddress(AddressDTO addressDTO) {
        AddressEntity addressEntity = addressMapper.toEntity(addressDTO);
        return addressRepository.save(addressEntity);
    }
}
