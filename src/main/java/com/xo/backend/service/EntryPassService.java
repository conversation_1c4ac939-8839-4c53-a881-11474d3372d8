package com.xo.backend.service;

import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.repository.booking.BookingTierRepository;
import com.xo.backend.database.repository.entry_pass.EntryPassCount;
import com.xo.backend.database.repository.entry_pass.EntryPassRepository;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.database.service.BookingTierAttributeService;
import com.xo.backend.error.exceptions.*;
import com.xo.backend.model.ConsumptionResult;
import com.xo.backend.model.dto.EntryPassConsumptionResponseDTO;
import com.xo.backend.model.dto.EntryPassInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.xo.backend.database.entity.entrypass.EntryPassUsageEnum.NOT_USED;
import static com.xo.backend.database.entity.entrypass.EntryPassUsageEnum.USED;
import static com.xo.backend.utlis.BookingReferenceGenerator.generateReferenceNumber;
import static com.xo.backend.utlis.Constants.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class EntryPassService {

    private final BookingRepository bookingRepository;
    private final EntryPassRepository entryPassRepository;
    private final BookingTierAttributeService bookingTierAttributeService;
    private final BookingTierRepository bookingTierRepository;
    private final EventRepository eventRepository;
    private final AdminBookingTierService adminBookingTierService;

    private static final String ENTRY_PASS_USED = "Entry pass already used";

    @Transactional(readOnly = true)
    public EntryPassInfoDTO getEntryPassInfo(String venueId, Integer eventId, UUID entryPassId) {

        validateVenuePermission(venueId, eventId);

        EntryPassEntity entryPass = entryPassRepository.findById(entryPassId)
                .orElseThrow(() -> new EntryPassNotFoundException("Entry pass not found"));

        validateEventAssociation(eventId, entryPass);
        validateBookingStatus(entryPass.getBooking());

        return constructEntryPassInfo(entryPass, entryPass.getBooking());
    }

    @Transactional
    public EntryPassConsumptionResponseDTO consumeEntryPass(String venueId, Integer eventId, UUID entryPassId) {

        validateVenuePermission(venueId, eventId);

        EntryPassEntity entryPass = entryPassRepository.findById(entryPassId)
                .orElseThrow(() -> new EntryPassNotFoundException("Entry pass not found"));

        validateEventAssociation(eventId, entryPass);
        validateBookingStatus(entryPass.getBooking());

        BookingEntity booking = entryPass.getBooking();

        if (USED.equals(entryPass.getUsed())) {
            return EntryPassConsumptionResponseDTO.builder()
                    .consumptionResult(ConsumptionResult.FAILURE)
                    .entryPassInfo(constructEntryPassInfo(entryPass, booking))
                    .consumptionFailReason(ENTRY_PASS_USED)
                    .build();
        }

        entryPass.setUsed(USED);
        entryPass.setUsedAt(Instant.now());
        entryPass = entryPassRepository.save(entryPass);

        if (booking.getStatus() != BookingStatus.ARRIVED) {
            booking.setStatus(BookingStatus.ARRIVED);
            booking.setArrivalTime(Instant.now());
            bookingRepository.save(booking);
        }

        return EntryPassConsumptionResponseDTO.builder()
                .consumptionResult(ConsumptionResult.SUCCESS)
                .entryPassInfo(constructEntryPassInfo(entryPass, booking))
                .build();
    }

    private void validateVenuePermission(String venueId, Integer eventId) {
        if (!eventRepository.existsByIdAndVenueId(eventId, venueId)) {
            throw new InsufficientPermissionsException();
        }
    }

    private EntryPassInfoDTO constructEntryPassInfo(EntryPassEntity entryPass, BookingEntity booking) {

        EntryPassCount entryPassCount = entryPassRepository.getEntryPassCountsByBookingId(booking.getId());

        BookingTierEntity bookingTier = entryPass.getBookingTier();

        BigDecimal includedConsumptionAmount = Optional.ofNullable(NumberUtils
                        .createBigDecimal(getBookingTierAttributeValue(bookingTier, INCLUDED_CONSUMPTION_AMOUNT)))
                .orElse(BigDecimal.ZERO);

        String includedConsumptionDescription = getBookingTierAttributeValue(bookingTier, INCLUDED_CONSUMPTION_DESCRIPTION);

        String bookingTierName = getBookingTierAttributeValue(bookingTier, TIER_NAME);

        BigDecimal minimumSpent = Optional.ofNullable(NumberUtils
                        .createBigDecimal(getBookingTierAttributeValue(bookingTier, MINIMUM_SPENT)))
                .orElse(BigDecimal.ZERO);

        BookingType bookingType = booking.getBookingType();

        return EntryPassInfoDTO.builder()
                .entryPassId(entryPass.getId())
                .entryPassReferenceNumber(entryPass.getReferenceNumber())
                .bookingType(bookingType)
                .bookingTierName(bookingTierName)
                .bookingNumber(booking.getBookingNumber())
                .bookingId(booking.getId())
                .reservationName(booking.getBookingName())
                .payOnlinePrice(bookingType.equals(BookingType.TICKET) ? entryPass.getPrice() : bookingTier.getPayOnlinePrice())
                .minimumSpent(minimumSpent)
                .includedConsumptionAmount(includedConsumptionAmount)
                .includedConsumptionDescription(includedConsumptionDescription)
                .totalScannedPasses(entryPassCount.getUsedCount())
                .totalBookingPasses(entryPassCount.getTotalCount())
                .isUsed(USED.equals(entryPass.getUsed()))
                .reservationsCount(bookingTier.getType() == BookingTierType.RESERVATION ? booking.getBookingItems().stream().map(BookingItemEntity::getQuantity).reduce(0, Integer::sum) : 0)
                .notes(booking.getNotes())
                .specialRequests(booking.getComment())
                .occasion(booking.getBookingOccasion())
                .build();
    }

    @Transactional
    public void deleteEntryPasses(BookingEntity booking) {
        entryPassRepository.deleteAll(entryPassRepository.findEntryPassEntitiesByBooking(booking));
        log.info("Entry passes deleted for booking {}", booking.getId());
    }

    private void validateEventAssociation(Integer eventId, EntryPassEntity entryPass) {
        Integer associatedEventId = entryPass.getBooking().getEventId();
        if (!associatedEventId.equals(eventId)) {
            throw new EventAssociationException("Entry pass does not belong to the specified event");
        }
    }

    private void validateBookingStatus(BookingEntity booking) {
        if (booking.getStatus() != BookingStatus.CONFIRMED && booking.getStatus() != BookingStatus.ARRIVED) {
            throw new BookingStatusException("Booking status must be CONFIRMED or ARRIVED");
        }
    }

    private String getBookingTierAttributeValue(BookingTierEntity tier, String attributeName) {
        BookingTierAttributeEntity attribute = bookingTierAttributeService
                .getBookingTierAttributeByName(tier.getBookingTierAttributes(), attributeName);
        return attribute != null ? attribute.getAttributeValue() : null;
    }

    @Transactional
    public void createEntryPassesAndUpdateAvailability(BookingEntity booking) {
        createEntryPasses(booking);
        adminBookingTierService.updateBookingAvailability(booking);
    }

    public void createEntryPasses(BookingEntity booking) {
        // Create entry passes for each booking item
        List<BookingItemEntity> bookingItems = booking.getBookingItems();
        List<EntryPassEntity> toBeCreatedEntryPasses = new ArrayList<>();
        for (BookingItemEntity bookingItem : bookingItems) {
            // get booking tier from booking item
            BookingTierEntity bookingTier = bookingTierRepository.findById(bookingItem.getBookingTierId()).orElseThrow();
            List<EntryPassEntity> entryPassEntityList = entryPassRepository.findByBookingAndBookingTier(booking, bookingItem.getBookingTier());
            BookingTierType bookingTierType = bookingTier.getType();
            switch (bookingTierType) {
                case TICKET -> {
                    // Handle TICKET booking type
                    for (int i = entryPassEntityList.size(); i < bookingItem.getQuantity(); i++) {
                        createEntryPass(booking, toBeCreatedEntryPasses, bookingTier);
                    }
                }
                case RESERVATION -> {
                    // Handle RESERVATION booking type
                    for (int i = entryPassEntityList.size(); i < bookingItem.getNumOfPeople(); i++) {
                        createEntryPass(booking, toBeCreatedEntryPasses, bookingTier);
                    }
                }
                default -> throw new InvalidBookingTypeException("Unknown booking type");
            }

        }
        entryPassRepository.saveAll(toBeCreatedEntryPasses);
    }

    private void createEntryPass(BookingEntity booking, List<EntryPassEntity> entryPassEntityList, BookingTierEntity bookingTier) {

        EntryPassEntity entryPass = EntryPassEntity.builder()
                .booking(booking)
                .eventId(booking.getEventId())
                .ownerId(booking.getOwnerId())
                .referenceNumber(generateReferenceNumber())
                .bookingTier(bookingTier)
                .ownerName(booking.getOwnerName())
                .used(NOT_USED)
                .price(calculateEntryPassPrice(booking, bookingTier))
                .build();

        entryPassEntityList.add(entryPass);
    }

    // need to remake this
    private BigDecimal calculateEntryPassPrice(BookingEntity booking, BookingTierEntity bookingTier) {
        BigDecimal entryPassPrice;
        switch (booking.getBookingType()) {
            case RESERVATION -> entryPassPrice = Optional.ofNullable(NumberUtils
                            .createBigDecimal(getBookingTierAttributeValue(bookingTier, INCLUDED_CONSUMPTION_AMOUNT)))
                    .orElse(BigDecimal.ZERO);
            case TICKET -> entryPassPrice = bookingTier.getPayOnlinePrice();
            case MIXED -> throw new InvalidBookingTypeException("Mixed booking type not supported");
            default -> throw new InvalidBookingTypeException("Unknown booking type");
        }
        return entryPassPrice;
    }
}
