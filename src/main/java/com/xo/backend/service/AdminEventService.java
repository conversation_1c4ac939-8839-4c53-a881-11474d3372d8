package com.xo.backend.service;

import com.opencsv.CSVWriter;
import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.address.AddressEntity;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.entity.bookings.BookingTierEntity;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.entity.events.EventStatus;
import com.xo.backend.database.repository.booking.BookingItemRepository;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.repository.booking.BookingTierRepository;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.database.service.EventAttributeService;
import com.xo.backend.error.exceptions.*;
import com.xo.backend.mappers.EventDetailsMapper;
import com.xo.backend.mappers.EventUpdateRequestToEntityMapper;
import com.xo.backend.model.EventBookingStatisticsPerDate;
import com.xo.backend.model.EventStatistics;
import com.xo.backend.model.EventTierStatistics;
import com.xo.backend.model.customer.CustomerCsvHeader;
import com.xo.backend.model.customer.CustomerCsvMapper;
import com.xo.backend.model.dto.BookingAttendanceDTO;
import com.xo.backend.model.dto.EventDetailsDTO;
import com.xo.backend.model.dto.EventStatisticsDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.request.event.CreateEventRequestDTO;
import com.xo.backend.model.dto.request.event.UpdateEventRequestDTO;
import com.xo.backend.model.dto.responses.CreateEventResponseDTO;
import jakarta.persistence.Tuple;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.xo.backend.database.entity.bookings.BookingStatus.DRAFT;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminEventService {

    public static final String STATUS = "status";
    public static final String COUNT = "count";

    private final EventRepository eventRepository;
    private final EventDetailsMapper eventMapper;
    private final EventUpdateRequestToEntityMapper eventUpdateRequestToEntityMapper;
    private final BookingRepository bookingRepository;
    private final EventAttributeService eventAttributeService;
    private final AdminBookingTierService bookingTierService;
    private final BookingItemRepository bookingItemRepository;
    private final BookingTierRepository bookingTierRepository;
    private final AddressService addressService;
    private final VenueInfoService venueInfoService;
    private final CustomerCsvMapper customerCsvMapper;
    private final WorkbookService workbookService;

    @Transactional
    public CreateEventResponseDTO createEvent(String venueId, CreateEventRequestDTO requestDTO) {

        validateEvent(requestDTO);

        EventEntity newEvent = createEventBlueprint(venueId, requestDTO);

        // Check if custom address should be used and validate
        if (Boolean.TRUE.equals(requestDTO.useCustomAddress())) {
            if (requestDTO.customAddress() == null) {
                log.error("Custom address is required but not provided for useCustomAddress=true");
                throw new CustomAddressRequestException("Custom address must be provided when useCustomAddress is set to true.");
            }
            AddressEntity customAddress = addressService.createOrUpdateAddress(requestDTO.customAddress());
            newEvent.setCustomAddress(customAddress);
            newEvent.setUseCustomAddress(true);
        }

        List<EventAttributeEntity> eventAttributeList = eventAttributeService.createEventAttributeList(newEvent, requestDTO);

        if (!CollectionUtils.isEmpty(requestDTO.bookingTiers())) {
            List<BookingTierEntity> bookingTierEntityList = bookingTierService.createBookingTierLists(newEvent, requestDTO.bookingTiers());
            newEvent.setBookingTiers(bookingTierEntityList);
        }

        newEvent.setEventAttributes(eventAttributeList);

        EventEntity savedEvent = eventRepository.save(newEvent);

        log.info("Event created: {}", savedEvent);
        log.info("Event attributes: {}", savedEvent.getEventAttributes());

        return new CreateEventResponseDTO(savedEvent.getId());
    }

    /**
     * This method validates published events only , event start date should not be in the past
     *
     * @param requestDTO Event request DTO
     */
    private void validateEvent(CreateEventRequestDTO requestDTO) {
        if (requestDTO.startDatetime().isBefore(OffsetDateTime.now())) {
            throw new EventRequestException("Published Event start date shouldn't be in the past");
        }
        if (requestDTO.endDatetime().isBefore(requestDTO.startDatetime())) {
            throw new EventRequestException("End date should be greater than start date");
        }
        validateBookingDeadline(requestDTO.endDatetime(), requestDTO.bookingDeadline());
    }


    private EventEntity createEventBlueprint(String venueId, CreateEventRequestDTO requestDTO) {
        return EventEntity.builder()
                .venueId(venueId)
                .status(requestDTO.status())
                .displayPrice(requestDTO.displayPrice())
                .allowPreSaleDiscountedPrices(requestDTO.allowPreSaleDiscountedPrices())
                .useCustomAddress(Boolean.TRUE.equals(requestDTO.useCustomAddress()))
                .startDatetime(requestDTO.startDatetime().toInstant())
                .endDatetime(requestDTO.endDatetime().toInstant())
                .bookingDeadline(Optional.ofNullable(requestDTO.bookingDeadline()).map(OffsetDateTime::toInstant).orElse(requestDTO.endDatetime().toInstant()))
                .build();
    }

    @Transactional(readOnly = true)
    public Page<EventDetailsDTO> getEventsByVenueId(String venueId, String status, Pageable pageable) {

        String capitalizedStatus = Optional.ofNullable(status)
                .map(String::toUpperCase)
                .orElse(null);

        Page<EventEntity> eventEntities;
        switch (capitalizedStatus) {

            case null -> eventEntities = eventRepository.findByVenueId(venueId, pageable);
            case "DRAFT" ->
                    eventEntities = eventRepository.findByVenueIdAndStatus(venueId, EventStatus.DRAFT, pageable);
            case "PUBLISHED" ->
                    eventEntities = eventRepository.findByVenueIdAndStatus(venueId, EventStatus.PUBLISHED, pageable);
            case "UPCOMING" ->
                    eventEntities = eventRepository.findByVenueIdAndStatusAndStartDatetimeGreaterThan(venueId, EventStatus.PUBLISHED, Instant.now(), pageable);
            case "ONGOING" ->
                    eventEntities = eventRepository.findByVenueIdAndStatusAndStartDatetimeLessThanEqualAndEndDatetimeGreaterThan(venueId, EventStatus.PUBLISHED, Instant.now(), Instant.now(), pageable);
            case "PAST" ->
                    eventEntities = eventRepository.findByVenueIdAndStatusAndEndDatetimeLessThanEqual(venueId, EventStatus.PUBLISHED, Instant.now(), pageable);
            case "CANCELLED" ->
                    eventEntities = eventRepository.findByVenueIdAndStatus(venueId, EventStatus.CANCELLED, pageable);
            default -> {
                return Page.empty();
            }
        }
        VenueDTO venueDTO = venueInfoService.getVenueDetails(venueId);
        return eventEntities.map((EventEntity eventEntity) -> eventMapper.mapToEventDetailsDTO(eventEntity, venueDTO));
    }

    @Transactional(readOnly = true)
    public EventDetailsDTO getEventById(String venueId, Integer eventId) {
        EventEntity event = eventRepository.findById(eventId).orElseThrow(EventNotFoundException::new);

        if (!event.getVenueId().equals(venueId)) {
            throw new InsufficientPermissionsException();
        }
        return eventMapper.mapToEventDetailsDTO(event, venueInfoService.getVenueDetails(venueId));
    }

    @Transactional
    public EventDetailsDTO updateEventPartially(String venueId, Integer eventId, UpdateEventRequestDTO requestDTO) {

        return updateEventBlueprint(venueId, eventId, requestDTO, false);
    }

    @Transactional
    public EventDetailsDTO updateEvent(String venueId, Integer eventId, UpdateEventRequestDTO requestDTO) {

        return updateEventBlueprint(venueId, eventId, requestDTO, true);
    }

    private EventDetailsDTO updateEventBlueprint(String venueId, Integer eventId, UpdateEventRequestDTO requestDTO, boolean isFullUpdate) {
        EventEntity event = eventRepository.findById(eventId).orElseThrow(EventNotFoundException::new);

        if (!event.getVenueId().equals(venueId)) {
            throw new InsufficientPermissionsException();
        }

        validateEventDates(requestDTO.startDatetime(), requestDTO.endDatetime(), requestDTO.bookingDeadline());

        handleStatusTransition(event, requestDTO.status());

        event = eventUpdateRequestToEntityMapper.apply(event, requestDTO, isFullUpdate);
        event = eventRepository.save(event);
        return eventMapper.mapToEventDetailsDTO(event, venueInfoService.getVenueDetails(venueId));
    }

    private void validateEventDates(OffsetDateTime startDatetime, OffsetDateTime endDatetime, OffsetDateTime bookingDeadline) {
        OffsetDateTime now = OffsetDateTime.now();
        if (startDatetime != null && startDatetime.isBefore(now)) {
            throw new IllegalArgumentException("Start date/time must be in the future");
        }
        if (endDatetime != null && endDatetime.isBefore(now)) {
            throw new IllegalArgumentException("End date/time must be in the future");
        }
        if (startDatetime != null && endDatetime != null && endDatetime.isBefore(startDatetime)) {
            throw new IllegalArgumentException("End date/time must be after start date/time");
        }
        validateBookingDeadline(endDatetime, bookingDeadline);
    }

    private void validateBookingDeadline(OffsetDateTime endDatetime, OffsetDateTime bookingDeadline) {
        if (bookingDeadline != null && bookingDeadline.isAfter(endDatetime)) {
            throw new IllegalArgumentException("booking deadline must be before end date/time");
        }
    }

    private void handleStatusTransition(EventEntity event, EventStatus newStatus) {
        EventStatus currentStatus = event.getStatus();
        if (newStatus == null || currentStatus == newStatus) {
            return;
        }

        switch (currentStatus) {
            case DRAFT:
                if (newStatus == EventStatus.CANCELLED) {
                    throw new IllegalArgumentException("To cancel a draft event, delete it instead");
                }
                break;

            case PUBLISHED:
                if (newStatus == EventStatus.CANCELLED) {
                    cancelAllBookings(event);
                } else if (newStatus == EventStatus.DRAFT && hasBookings(event)) {
                    throw new IllegalArgumentException("Cannot revert to draft as bookings exist");
                }
                break;

            case CANCELLED:
                if (newStatus == EventStatus.PUBLISHED) {
                    throw new IllegalArgumentException("Cannot publish a cancelled event");
                }
                break;
            default:
                break;
        }

        event.setStatus(newStatus);
    }

    private void cancelAllBookings(EventEntity event) {
        bookingRepository.markAllAsCancelledForEvent(event.getId());
    }

    private boolean hasBookings(EventEntity event) {
        return bookingRepository.countByEventId(event.getId()) > 0;
    }

    @Transactional
    public void deleteEvent(String venueId, Integer eventId) {

        if (!eventRepository.existsByIdAndVenueId(eventId, venueId)) {
            throw new InsufficientPermissionsException();
        }

        if (bookingRepository.existsByEventId(eventId)) {
            throw new EventRequestException("Cannot delete the event because it has associated purchases.");
        }
        eventRepository.deleteById(eventId);
    }

    @Transactional(readOnly = true)
    public EventStatisticsDTO getEventStatistics(String venueId, Integer eventId) {

        if (!eventRepository.existsByIdAndVenueId(eventId, venueId)) {
            throw new InsufficientPermissionsException();
        }

        List<EventBookingStatisticsPerDate> eventBookingStatisticsPerDates = bookingItemRepository.getDailyBookingItemStatistics(eventId);
        List<EventTierStatistics> eventTierStatistics = bookingItemRepository.getEventTierStatistics(eventId);
        EventStatistics eventStatistics = bookingItemRepository.getEventStatistics(eventId, bookingTierRepository.getAvailableQuantityByEventId(eventId));

        return EventStatisticsDTO.builder()
                .eventBookingStatisticsPerDates(eventBookingStatisticsPerDates)
                .eventTierStatistics(eventTierStatistics)
                .eventStatistics(eventStatistics)
                .build();

    }

    @Transactional(readOnly = true)
    public BookingAttendanceDTO getEventAttendance(String venueId, Integer eventId) {

        if (!eventRepository.existsByIdAndVenueId(eventId, venueId)) {
            throw new InsufficientPermissionsException();
        }

        List<Tuple> tuples = bookingRepository.getBookingAttendance(eventId);
        int bookingsMadeCount = 0;
        int bookingsArrivedCount = 0;
        for (Tuple tuple : tuples) {
            BookingStatus status = tuple.get(STATUS, BookingStatus.class);
            Long count = tuple.get(COUNT, Long.class);
            if (BookingStatus.ARRIVED.equals(status)) {
                bookingsArrivedCount += count;
                bookingsMadeCount += count;
            } else if (!DRAFT.equals(status)) {
                bookingsMadeCount += count;
            }
        }
        return BookingAttendanceDTO.builder().bookingsMadeCount(bookingsMadeCount).bookingsArrivedCount(bookingsArrivedCount).build();
    }

    @Transactional(readOnly = true)
    public byte[] exportCustomerAsCsv(String venueId, Integer eventId) {

        if (!eventRepository.existsByIdAndVenueId(eventId, venueId)) {
            throw new InsufficientPermissionsException();
        }

        StringWriter writer = new StringWriter();
        try (CSVWriter csvWriter = new CSVWriter(writer)) {
            csvWriter.writeNext(CustomerCsvHeader.getAllHeaders());

            List<BookingStatus> bookingStatuses = List.of(BookingStatus.CONFIRMED, BookingStatus.ARRIVED, BookingStatus.APPROVED);

            bookingRepository.findAllByEventIdAndStatusIn(eventId, bookingStatuses)
                    .forEach(bookingEntity -> csvWriter.writeNext(customerCsvMapper.apply(bookingEntity).getRowValues()));

            return writer.toString().getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new CsvExportException(e.getMessage());
        }
    }

    @Transactional(readOnly = true)
    public byte[] exportCustomerAsXlsx(String venueId, Integer eventId) {

        if (!eventRepository.existsByIdAndVenueId(eventId, venueId)) {
            throw new InsufficientPermissionsException();
        }

        List<BookingStatus> bookingStatuses = List.of(BookingStatus.CONFIRMED, BookingStatus.ARRIVED, BookingStatus.APPROVED);
        List<String[]> customerData = new ArrayList<>();

        bookingRepository.findAllByEventIdAndStatusIn(eventId, bookingStatuses)
                .forEach(bookingEntity -> customerData
                        .add(customerCsvMapper
                                .apply(bookingEntity)
                                .getRowValues()));


        return workbookService.createSingleSheetXlsxWorkbook("Customer Bookings", CustomerCsvHeader.getAllHeaders(), customerData);

    }
}
