package com.xo.backend.service;

import com.xo.backend.database.entity.AttributeEntity;
import com.xo.backend.database.entity.LanguageEntity;
import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.repository.booking.BookingItemRepository;
import com.xo.backend.database.repository.booking.BookingTierRepository;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.database.service.AttributeService;
import com.xo.backend.database.service.BookingTierAttributeService;
import com.xo.backend.database.service.LanguageService;
import com.xo.backend.error.exceptions.*;
import com.xo.backend.mappers.BookingTierMapper;
import com.xo.backend.model.dto.BookingTierDTO;
import com.xo.backend.utlis.SecurityContextUtil;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.xo.backend.database.entity.bookings.BookingTierType.RESERVATION;

import com.google.common.collect.Range;

import static com.xo.backend.utlis.Constants.*;
import static com.xo.backend.utlis.NullSafeUtils.setIfNotNull;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminBookingTierService {

    private final AttributeService attributeService;
    private final LanguageService languageService;
    private final BookingTierAttributeService bookingTierAttributeService;
    private final BookingTierRepository bookingTierRepository;
    private final BookingItemRepository bookingItemRepository;
    private final EventRepository eventRepository;
    private final BookingTierMapper bookingTierMapper;
    private Map<String, AttributeEntity> attributeMap;
    private Map<String, LanguageEntity> languageMap;

    @PostConstruct
    public void init() {
        attributeMap = attributeService.getAttributeEntityMap(BOOKING_TIER_ATTRIBUTES);

        languageMap = languageService.getLanguageEntityMap(List.of(NO_LANG, ENGLISH_LANG));

    }

    public List<BookingTierEntity> createBookingTierLists(EventEntity newEvent, List<BookingTierDTO> bookingTierDTOS) {
        List<BookingTierEntity> bookingTierEntityList = new ArrayList<>();

        for (BookingTierDTO tier : bookingTierDTOS) {

            BookingTierEntity newBookingTier = createNewBookingTier(newEvent, tier);

            bookingTierEntityList.add(newBookingTier);

        }
        return bookingTierEntityList;
    }

    /**
     * This method creates a new booking tier Entity without persisting it
     */
    private BookingTierEntity createNewBookingTier(EventEntity newEvent, BookingTierDTO bookingTierDTO) {
        BookingTierEntity bookingTierEntity = BookingTierEntity.builder()
                .payOnlinePrice(Optional.ofNullable(bookingTierDTO.payOnlinePrice()).orElse(BigDecimal.ZERO))
                .type(bookingTierDTO.type())
                .event(newEvent)
                .minPersons(bookingTierDTO.minPersons())
                .maxPersons(bookingTierDTO.maxPersons())
                .availableQty(bookingTierDTO.availability())
                .requireConfirmation(bookingTierDTO.requireConfirmation())
                .isVisible(Optional.ofNullable(bookingTierDTO.isVisible()).orElse(true))
                .preSaleDiscountedPrice(
                        Optional.ofNullable(newEvent.getAllowPreSaleDiscountedPrices())
                                .filter(Boolean::booleanValue)
                                .map(ignored ->
                                        bookingTierDTO.preSaleDiscountedPrice() != null
                                                ? bookingTierDTO.preSaleDiscountedPrice()
                                                : bookingTierDTO.payOnlinePrice()
                                )
                                .orElse(null)).build();

        List<BookingTierAttributeEntity> bookingTierAttributeEntityList = bookingTierAttributeService
                .createBookingTierAttributeList(bookingTierEntity, bookingTierDTO, attributeMap, languageMap);

        bookingTierEntity.setBookingTierAttributes(bookingTierAttributeEntityList);

        log.info("booking tier created: {}", bookingTierEntity);

        return bookingTierEntity;
    }

    /**
     * This method create a booking tier entity and assign it to an event , then persist it
     */
    @Transactional
    public BookingTierDTO createBookingTierEntity(String venueId, Integer eventId, BookingTierDTO bookingTierDTO) {
        EventEntity event = eventRepository.findById(eventId).orElseThrow(EventNotFoundException::new);

        validateEventPermissions(event, venueId);

        BookingTierEntity bookingTierEntity = bookingTierRepository.save(createNewBookingTier(event, bookingTierDTO));

        return bookingTierMapper.mapToBookingTierDTO(bookingTierEntity);
    }

    private void validateEventPermissions(EventEntity event, String venueId) {
        if (!event.getVenueId().equals(venueId)) {
            throw new InsufficientPermissionsException();
        }
    }

    @Transactional
    public BookingTierDTO updateBookingTierEntity(String venueId, Integer bookingTierId, BookingTierDTO bookingTierDTO) {
        BookingTierEntity bookingTierEntity = bookingTierRepository.findById(bookingTierId).orElseThrow(BookingTierNotFoundException::new);

        validateEventPermissions(bookingTierEntity.getEvent(), venueId);

        if (bookingTierDTO.payOnlinePrice() != null &&
                bookingTierEntity.getPayOnlinePrice() != null &&
                bookingTierDTO.payOnlinePrice().compareTo(bookingTierEntity.getPayOnlinePrice()) != 0) {
            throw new PriceModificationException("payOnlinePrice cannot be modified.");
        }

        setIfNotNull(bookingTierDTO.type(), bookingTierEntity::setType);
        setIfNotNull(bookingTierDTO.minPersons(), bookingTierEntity::setMinPersons);
        setIfNotNull(bookingTierDTO.maxPersons(), bookingTierEntity::setMaxPersons);
        setIfNotNull(bookingTierDTO.availability(), bookingTierEntity::setAvailableQty);
        setIfNotNull(bookingTierDTO.requireConfirmation(), bookingTierEntity::setRequireConfirmation);
        setIfNotNull(bookingTierDTO.isVisible(), bookingTierEntity::setIsVisible);
        if(bookingTierDTO.preSaleDiscountedPrice() != null && Boolean.TRUE.equals(bookingTierEntity.getEvent().getAllowPreSaleDiscountedPrices())) {
            bookingTierEntity.setPreSaleDiscountedPrice(bookingTierDTO.preSaleDiscountedPrice());
        }

        if (bookingTierDTO.eventId() != null) {
            EventEntity event = eventRepository.findById(bookingTierDTO.eventId()).orElseThrow(EventNotFoundException::new);
            validateEventPermissions(event, venueId);
            bookingTierEntity.setEvent(event);
        }

        bookingTierAttributeService.updateBookingTierAttributeList(bookingTierEntity, bookingTierDTO);

        bookingTierEntity = bookingTierRepository.save(bookingTierEntity);

        log.info("booking tier updated: {}", bookingTierEntity);

        return bookingTierMapper.mapToBookingTierDTO(bookingTierEntity);
    }

    @Transactional
    public void deleteBookingTierEntity(String venueId, Integer bookingTierId) {

        if (!bookingTierRepository.existsByIdAndEventVenueId(bookingTierId, venueId)) {
            throw new InsufficientPermissionsException();
        }

        if (bookingItemRepository.existsByBookingTierId(bookingTierId)) {
            throw new BookingTierRequestException("Cannot delete the booking tier because it has associated purchases.");
        }


        bookingTierRepository.deleteById(bookingTierId);
    }

    @Transactional(readOnly = true)
    public BookingTierDTO getBookingTierDetails(String venueId, Integer bookingTierId) {

        if (!bookingTierRepository.existsByIdAndEventVenueId(bookingTierId, venueId)) {
            throw new InsufficientPermissionsException();
        }

        BookingTierEntity bookingTierEntity = bookingTierRepository.findById(bookingTierId).orElseThrow(BookingTierNotFoundException::new);

        return bookingTierMapper.mapToBookingTierDTO(bookingTierEntity);
    }

    @Transactional(readOnly = true)
    public List<BookingTierDTO> getEventBookingTiers(String venueId, Integer eventId) {

        if (!eventRepository.existsByIdAndVenueId(eventId, venueId)) {
            throw new InsufficientPermissionsException();
        }

        List<BookingTierEntity> bookingTierEntities = bookingTierRepository.findByEventId(eventId);

        return bookingTierEntities.stream().map(bookingTierMapper::mapToBookingTierDTO).toList();
    }

    @Transactional
    public void updateBookingAvailability(BookingEntity booking) {
        for (BookingItemEntity bookingItemEntity : booking.getBookingItems()) {
            checkBookingTierAvailability(bookingItemEntity.getBookingTierId(),
                    bookingItemEntity.getQuantity(), bookingItemEntity.getNumOfPeople(), booking.getEventId(), true);
        }
    }

    public void checkBookingTierAvailability(int bookingTierId, int quantity, Integer numberOfPeople, int eventId, boolean updateAvailability) {

        BookingTierEntity bookingTier = bookingTierRepository.findAndLockTierById(bookingTierId)
                .orElseThrow(BookingNotFoundException::new);

        if (bookingTier.getEventId() != eventId) {
            throw new BookingRequestException("booking tier doesnt belong to this event");
        }

        boolean isAdmin = SecurityContextUtil.hasAdminOrStoreAdminPrivileges(bookingTier.getEvent().getVenueId());

        if (!isAdmin && bookingTier.getAvailableQty() < quantity) {
            throw new BookingRequestException("Insufficient availability for tier: " + bookingTierId);
        }

        if (!isAdmin && bookingTier.getType() == RESERVATION && !Range.closed(bookingTier.getMinPersons(), bookingTier.getMaxPersons() * quantity).contains(numberOfPeople)) {
            throw new BookingRequestException("Number of people is not within the min and max range for tier: " + bookingTierId);
        }

        if (updateAvailability) {

            int newAvailability = bookingTier.getAvailableQty() - quantity;
            bookingTier.setAvailableQty(newAvailability);

            if (isAdmin && newAvailability < 0) {
                log.warn("Admin override: Booking tier ID {} availability is now negative: {}", bookingTierId, newAvailability);
            } else if (newAvailability < 0) {
                throw new BookingRequestException("Insufficient availability. Cannot book tier: " + bookingTierId);
            }
            bookingTierRepository.save(bookingTier);
        }
    }

    public void revertBookingTiersAvailabilities(BookingEntity booking) {
        List<BookingTierEntity> bookingTiersToUpdate = new ArrayList<>();
        for (BookingItemEntity bookingItemEntity : booking.getBookingItems()) {
            BookingTierEntity bookingTier = bookingItemEntity.getBookingTier();
            bookingTier.setAvailableQty(bookingTier.getAvailableQty() + bookingItemEntity.getQuantity());
            bookingTiersToUpdate.add(bookingTier);
        }
        bookingTierRepository.saveAll(bookingTiersToUpdate);
    }
}
