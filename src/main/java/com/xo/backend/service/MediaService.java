package com.xo.backend.service;

import com.xo.backend.client.media.MediaAdapter;
import com.xo.backend.model.UploadFileType;
import com.xo.backend.model.dto.responses.MediaUploadResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@RequiredArgsConstructor
public class MediaService {


    private final MediaAdapter mediaAdapter;


    public MediaUploadResponseDTO uploadFile(MultipartFile file, UploadFileType uploadFileType) {
        String url = mediaAdapter.uploadFile(file,uploadFileType);
        return new MediaUploadResponseDTO(url);
    }


    public InputStreamResource downloadFile(String fileName) {
        return mediaAdapter.downloadFile(fileName);
    }

    public void deleteFile(String fileName) {
        mediaAdapter.deleteFile(fileName);
    }

    public MediaUploadResponseDTO replaceFile(MultipartFile file,String fileName,UploadFileType uploadFileType) {
        deleteFile(fileName);
        String url= mediaAdapter.uploadFile(file,uploadFileType);
        return new MediaUploadResponseDTO(url);
    }
}
