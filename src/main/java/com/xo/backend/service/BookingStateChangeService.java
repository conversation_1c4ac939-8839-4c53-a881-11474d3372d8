package com.xo.backend.service;

import com.xo.backend.client.email.EmailService;
import com.xo.backend.client.notification.NotificationService;
import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.service.PaymentService;
import com.xo.backend.error.exceptions.BookingNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.xo.backend.database.entity.bookings.BookingStatus.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class BookingStateChangeService {

    private final BookingRepository bookingRepository;
    private final PaymentService paymentService;
    private final EntryPassService entryPassService;
    private final AdminBookingTierService adminBookingTierService;
    private final EmailService emailService;
    private final NotificationService notificationService;

    @Transactional
    public void handleStateChange(Integer bookingId, BookingStatus oldStatus, BookingStatus newStatus) {
        BookingEntity booking = bookingRepository.findById(bookingId)
                .orElseThrow(BookingNotFoundException::new);

        booking.setStatus(newStatus); // Update the state

        if (oldStatus == APPROVED || isDraftToFinalStatus(oldStatus, newStatus)) {
            paymentService.expireCheckoutSession(booking.getId());
        }
        if (newStatus == CONFIRMED) {
            entryPassService.createEntryPassesAndUpdateAvailability(booking);
        } else if (oldStatus == CONFIRMED) {
            adminBookingTierService.revertBookingTiersAvailabilities(booking);
            entryPassService.deleteEntryPasses(booking);
        }
        log.info("Booking state changed to {}", newStatus);

        emailService.sendBookingEmail(booking);
        notificationService.sendNotifications(booking);
    }

    private boolean isDraftToFinalStatus(BookingStatus oldStatus, BookingStatus newStatus) {
        return oldStatus == DRAFT &&
                (newStatus == CONFIRMED || newStatus == DECLINED || newStatus == CANCELLED);
    }
}
