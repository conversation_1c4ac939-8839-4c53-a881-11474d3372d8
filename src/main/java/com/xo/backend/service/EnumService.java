package com.xo.backend.service;

import com.xo.backend.database.entity.bookings.BookingOccasion;
import com.xo.backend.model.dto.BookingOccasionDTO;
import com.xo.backend.model.dto.SupportEmailOptions;
import com.xo.backend.model.dto.SupportEmailOptionsDTO;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class EnumService {

    public List<BookingOccasionDTO> getBookingOccasionEnum() {
        return Arrays.stream(BookingOccasion.values())
                .map(occasion -> new BookingOccasionDTO(
                        occasion,
                        occasion.getDisplayValue()
                ))
                .toList();
    }


    public List<SupportEmailOptionsDTO> getSupportEmailOptionsEnum() {
        return Arrays.stream(SupportEmailOptions.values())
                .map(options -> new SupportEmailOptionsDTO(
                        options,
                        options.getDisplayName()
                ))
                .toList();
    }
}
