package com.xo.backend.log;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RequestResponseLoggingFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Generate a request ID for correlation
        String requestId = UUID.randomUUID().toString();

        // Wrap the request and response
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);

        logRequestMetadata(requestWrapper, requestId);

        filterChain.doFilter(requestWrapper, responseWrapper);

        logRequestBody(requestWrapper, requestId);
        logResponse(responseWrapper, requestId);

        responseWrapper.copyBodyToResponse();
    }

    private void logRequestMetadata(ContentCachingRequestWrapper request, String requestId) {
        String queryString = request.getQueryString() != null ? "?" + request.getQueryString() : "";

        log.info("[{}] > REQUEST: {} {}{}",
                requestId,
                request.getMethod(),
                request.getRequestURI(),
                queryString);

        String headers = Collections.list(request.getHeaderNames()).stream()
                .map(headerName -> headerName + "=" + request.getHeader(headerName))
                .collect(Collectors.joining(", "));

        log.debug("[{}] > HEADERS: {}", requestId, headers);
    }

    private void logRequestBody(ContentCachingRequestWrapper request, String requestId) {
        String requestBody = new String(request.getContentAsByteArray(), StandardCharsets.UTF_8);
        if (!requestBody.isEmpty()) {
            log.info("[{}] > Request Body: {}", requestId, requestBody);
        }
    }

    private void logResponse(ContentCachingResponseWrapper response, String requestId) {
        String responseBody = new String(response.getContentAsByteArray(), StandardCharsets.UTF_8);

        String headers = response.getHeaderNames().stream()
                .map(headerName -> headerName + "=" + response.getHeader(headerName))
                .collect(Collectors.joining(", "));

        log.info("[{}] < Response Status: {}", requestId, response.getStatus());

        // Log headers at debug level
        log.debug("[{}] < HEADERS: {}", requestId, headers);

        if (!responseBody.isEmpty()) {
            log.info("[{}] < Response Body: {}", requestId, responseBody);
        }
    }
}