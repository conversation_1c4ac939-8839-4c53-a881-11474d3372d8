package com.xo.backend.monitoring;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class AsyncEmailMonitor {

    @Qualifier("emailExecutor")
    private final ThreadPoolTaskExecutor emailExecutor;

    @Scheduled(fixedRate = 60000)
    public void monitorThreadPool() {
        log.info("Email Executor Stats - Active: {}, Pool: {}, Queue: {}, Completed: {}",
                emailExecutor.getActiveCount(),
                emailExecutor.getPoolSize(),
                emailExecutor.getQueueSize(),
                emailExecutor.getThreadPoolExecutor().getCompletedTaskCount()
        );
    }
}
