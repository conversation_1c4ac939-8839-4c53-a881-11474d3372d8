package com.xo.backend.specs;

import com.xo.backend.database.entity.bookings.BookingChannel;
import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.utlis.Constants;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import lombok.experimental.UtilityClass;
import org.springframework.data.jpa.domain.Specification;

@UtilityClass
public class BookingSpecs {

    public static Specification<BookingEntity> withEventName(String eventName) {
        return (root, query, cb) ->
        {
            if (eventName == null) {
                return cb.and();  // Return an empty conjunction if eventName is null.
            }

            Join<EventEntity, EventAttributeEntity> eventAttributeJoin = root.join("event").join("eventAttributes");

            Predicate isTitle = cb.equal(eventAttributeJoin.get("attribute").get("name"), Constants.TITLE);

            Predicate isEventNameMatch = cb.like(eventAttributeJoin.get("attributeValue"), eventName + "%");

            return cb.and(isTitle, isEventNameMatch);
        };
    }

    public static Specification<BookingEntity> withEventId(Integer eventId) {
        return (root, query, cb) -> eventId == null ? cb.and() : cb.equal(root.get("eventId"), eventId);
    }

    public static Specification<BookingEntity> withBookingId(Integer bookingId) {
        return (root, query, cb) -> bookingId == null ? cb.and() : cb.equal(root.get("id"), bookingId);
    }

    public static Specification<BookingEntity> withOwnerName(String ownerName) {
        return (root, query, cb) ->
                ownerName == null ? cb.and() : cb.like(root.get("ownerName"), ownerName + "%");
    }

    public static Specification<BookingEntity> withOwnerId(Integer ownerId) {
        return (root, query, cb) ->
                ownerId == null ? cb.and() : cb.equal(root.get("ownerId"), ownerId);
    }

    public static Specification<BookingEntity> withBookingStatus(BookingStatus bookingStatus) {
        return (root, query, cb) ->
                bookingStatus == null ? cb.and() : cb.equal(root.get("status"), bookingStatus);
    }

    public static Specification<BookingEntity> withVenueId(String venueId) {
        return (root, query, cb) -> venueId == null ? cb.and() : cb.equal(root.get("event").get("venueId"), venueId);
    }

    public static Specification<BookingEntity> withBookingNumber(String bookingNumber) {
        return (root, query, cb) ->
                bookingNumber == null ? cb.and() : cb.equal(root.get("bookingNumber"), bookingNumber);
    }
    public static Specification<BookingEntity> withBookingChannel(BookingChannel bookingChannel) {
        return (root, query, cb) -> cb.equal(root.get("bookingChannel"), bookingChannel);
    }

    public static Specification<BookingEntity> excludeDraftStatus() {
        return (root, query, cb) -> cb.notEqual(root.get("status"), BookingStatus.DRAFT);
    }
}
