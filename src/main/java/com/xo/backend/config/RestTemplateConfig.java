package com.xo.backend.config;

import com.xo.backend.client.go.GoProperties;
import com.xo.backend.log.LogRestTemplateInterceptor;
import lombok.RequiredArgsConstructor;
import org.apache.hc.client5.http.classic.HttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

@Configuration
@RequiredArgsConstructor
public class RestTemplateConfig implements WebMvcConfigurer {

    private final GoProperties goProperties;

    @Bean(name = "goRestClient")
    public RestTemplate goRestTemplate (HttpClient httpClient) {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectionRequestTimeout(goProperties.getTimeout());
        requestFactory.setConnectTimeout(goProperties.getTimeout());
        requestFactory.setHttpClient(httpClient);

        RestTemplate template = new RestTemplate(new BufferingClientHttpRequestFactory(requestFactory));

        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(new LogRestTemplateInterceptor());

        template.setInterceptors(interceptors);
        template.getMessageConverters().add(new FormHttpMessageConverter());

        return template;
    }

}
