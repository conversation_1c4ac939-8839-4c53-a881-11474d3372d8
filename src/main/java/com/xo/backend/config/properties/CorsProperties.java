package com.xo.backend.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "cors-config")
@Getter
@Setter
public class CorsProperties {

    private Map<String, Map<String,String>> capability;
}
