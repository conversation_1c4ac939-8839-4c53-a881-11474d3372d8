package com.xo.backend.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "http-connection-pool")
public class HttpConnectionPoolProperties {

    private int maxTotal;
    private int defaultMaxPerRoute;

}
