package com.xo.backend.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "bookings")
@Getter
@Setter
public class BookingProperties {

    private int maxTicketPerUserPerEvent; //this is per tier per event per user. works as expected, need name change
    private int maxReservationPerUserPerEvent;
    private boolean mixedBookingAllowed;
}
