package com.xo.backend.config;

import com.xo.backend.config.properties.HttpConnectionPoolProperties;
import lombok.RequiredArgsConstructor;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@RequiredArgsConstructor
public class HttpConfig implements WebMvcConfigurer {

    private final HttpConnectionPoolProperties poolProperties;

    @Bean
    public PoolingHttpClientConnectionManager poolingHttpClientConnectionManager() {
        PoolingHttpClientConnectionManager clientConnectionManager = new PoolingHttpClientConnectionManager();
        clientConnectionManager.setDefaultMaxPerRoute(poolProperties.getDefaultMaxPerRoute());
        clientConnectionManager.setMaxTotal(poolProperties.getMaxTotal());
        return clientConnectionManager;
    }

    @Bean
    public CloseableHttpClient httpClient(@Autowired PoolingHttpClientConnectionManager poolingHttpClientConnectionManager) {
        return HttpClientBuilder.create().setConnectionManager(poolingHttpClientConnectionManager).build();
    }
}
