package com.xo.backend.mappers;

import com.xo.backend.database.entity.bookings.BookingItemEntity;
import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.database.repository.entry_pass.EntryPassRepository;
import com.xo.backend.model.dto.BookingItemDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;


@Mapper(componentModel = "spring", uses = BookingTierMapper.class)
public abstract class BookingItemMapper {

    @Autowired
    private EntryPassRepository entryPassRepository;


    @Mapping(target = "bookingTier.availability", ignore = true)
    @Mapping(target = "totalAmount", source = "bookingItemEntity.totalPrice")
    public abstract BookingItemDTO mapToBookingItemDTO(BookingItemEntity bookingItemEntity);

    @AfterMapping
    protected void applyAttributes(BookingItemEntity bookingItemEntity,@MappingTarget BookingItemDTO.BookingItemDTOBuilder bookingItemDTOBuilder) {
        bookingItemDTOBuilder.entryPassIds(entryPassRepository.findByBooking_IdAndBookingTier_Id(bookingItemEntity.getBookingId(),bookingItemEntity.getBookingTierId()).stream().map(EntryPassEntity::getId).toList());
    }
}
