package com.xo.backend.mappers;

import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.service.EventAttributeService;
import com.xo.backend.error.exceptions.CustomAddressRequestException;
import com.xo.backend.model.dto.AddressDTO;
import com.xo.backend.model.dto.request.event.UpdateEventRequestDTO;
import com.xo.backend.service.AddressService;
import com.xo.backend.utlis.SecurityContextUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.function.TriFunction;
import org.springframework.stereotype.Component;


@Component
@RequiredArgsConstructor
public class EventUpdateRequestToEntityMapper implements TriFunction<EventEntity, UpdateEventRequestDTO, Boolean, EventEntity> {

    private final EventAttributeService eventAttributeService;
    private final AddressService addressService;

    @Override
    public EventEntity apply(EventEntity eventEntity, UpdateEventRequestDTO updateEventRequestDTO, Boolean fullUpdate) {

        updateEventEntity(eventEntity, updateEventRequestDTO, fullUpdate);

        eventAttributeService.updateEventAttributes(eventEntity, updateEventRequestDTO, fullUpdate);

        return eventEntity;
    }


    private void updateEventEntity(EventEntity eventEntity, UpdateEventRequestDTO updateEventRequestDTO, boolean fullUpdate) {
        if (fullUpdate || updateEventRequestDTO.venueId() != null) {
            SecurityContextUtil.validateVenueAccess(updateEventRequestDTO.venueId());
            eventEntity.setVenueId(updateEventRequestDTO.venueId());
        }

        if (fullUpdate || updateEventRequestDTO.status() != null) {
            eventEntity.setStatus(updateEventRequestDTO.status());
        }

        if (fullUpdate || updateEventRequestDTO.displayPrice() != null) {
            eventEntity.setDisplayPrice(updateEventRequestDTO.displayPrice());
        }

        if(fullUpdate || updateEventRequestDTO.allowPreSaleDiscountedPrices()!=null){
            eventEntity.setAllowPreSaleDiscountedPrices(updateEventRequestDTO.allowPreSaleDiscountedPrices());
        }

        updateEventTimes(eventEntity, updateEventRequestDTO, fullUpdate);

        updateEventCustomAddress(eventEntity, updateEventRequestDTO, fullUpdate);
    }

    private void updateEventTimes(EventEntity eventEntity, UpdateEventRequestDTO updateEventRequestDTO, boolean fullUpdate) {
        if (fullUpdate || updateEventRequestDTO.startDatetime() != null) {
            eventEntity.setStartDatetime(updateEventRequestDTO.startDatetime().toInstant());
        }

        if (fullUpdate || updateEventRequestDTO.endDatetime() != null) {
            eventEntity.setEndDatetime(updateEventRequestDTO.endDatetime().toInstant());
        }

        if (fullUpdate || updateEventRequestDTO.bookingDeadline() != null) {
            eventEntity.setBookingDeadline(updateEventRequestDTO.bookingDeadline().toInstant());
        }
    }

    private void updateEventCustomAddress(EventEntity eventEntity, UpdateEventRequestDTO updateEventRequestDTO, boolean fullUpdate) {
        if (fullUpdate || updateEventRequestDTO.customAddress() != null) {
            AddressDTO customAddress = updateEventRequestDTO.customAddress();
            eventEntity.setCustomAddress(customAddress == null ? null : addressService.createOrUpdateAddress(customAddress));
        }

        if (fullUpdate || updateEventRequestDTO.useCustomAddress() != null) {
            if (Boolean.TRUE.equals(updateEventRequestDTO.useCustomAddress()) && eventEntity.getCustomAddress() == null) {
                throw new CustomAddressRequestException("Custom address must be provided when useCustomAddress is set to true.");
            }
            eventEntity.setUseCustomAddress(updateEventRequestDTO.useCustomAddress());
        }
    }
}
