package com.xo.backend.mappers;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.model.dto.BookingDTO;
import com.xo.backend.utlis.SecurityContextUtil;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {BookingItemMapper.class})
public interface BookingMapper {

    @Mapping(source = "id", target = "bookingId")
    @Mapping(source = "status", target = "bookingStatus")
    @Mapping(source = "bookingChannel", target = "bookingChannel")
    @Mapping(target = "notes",ignore = true)
    BookingDTO mapToBookingDTO(BookingEntity bookingEntity);

    @AfterMapping
    default void mapAdminOnlyField(BookingEntity bookingEntity, @MappingTarget BookingDTO.BookingDTOBuilder bookingDTOBuilder) {
        if(SecurityContextUtil.hasAdminOrStoreAdminPrivileges(bookingEntity.getEvent().getVenueId())){
            bookingDTOBuilder.notes(bookingEntity.getNotes());
        }
    }
}
