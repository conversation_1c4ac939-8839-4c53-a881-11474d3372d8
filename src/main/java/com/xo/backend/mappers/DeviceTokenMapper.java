package com.xo.backend.mappers;

import com.xo.backend.database.entity.notifications.DeviceTokenEntity;
import com.xo.backend.model.dto.notifications.DeviceTokenResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {MapperUtils.class})
public interface DeviceTokenMapper {

    @Mapping(target = "tokenExpiry", source = "tokenExpiry", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "lastNotificationSent", source = "lastNotificationSent", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "instantToOffsetDateTime")
    DeviceTokenResponse toDto(DeviceTokenEntity entity);
}
