package com.xo.backend.mappers;

import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;

@Component
public class MapperUtils {

    // Custom conversion methods
    @Named("instantToOffsetDateTime")
     public static OffsetDateTime instantToOffsetDateTime(Instant instant) {
        return instant != null ? OffsetDateTime.ofInstant(instant, ZoneId.of("UTC")) : null;
    }

    private MapperUtils() {}
}
