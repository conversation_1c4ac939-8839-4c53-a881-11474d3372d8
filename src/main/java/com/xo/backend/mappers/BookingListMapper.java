package com.xo.backend.mappers;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.model.dto.AddressDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.responses.BookingListResponse;
import io.jsonwebtoken.lang.Collections;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import static com.xo.backend.utlis.Constants.BANNER;
import static com.xo.backend.utlis.Constants.TITLE;

@Mapper(componentModel = "spring", uses = MapperUtils.class)
public abstract class BookingListMapper {
    
    @Mapping(target = "eventId", source = "booking.eventId")
    @Mapping(target = "eventStatus", source = "booking.event.status")
    @Mapping(target = "eventBanner", ignore = true)  // Will be set in @AfterMapping
    @Mapping(target = "eventTitle", ignore = true)   // Will be set in @AfterMapping
    @Mapping(target = "eventDateTime", source = "booking.event.startDatetime", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "venueName", source = "addressDTO.addressName")
    @Mapping(target = "bookingType", source = "booking.bookingType")
    @Mapping(target = "status", source = "booking.status")
    @Mapping(target = "bookingName", source = "booking.bookingName")
    @Mapping(target = "hasEntryPasses", source = "hasEntryPasses")
    @Mapping(target = "bookingId", source = "booking.id")
    @Mapping(target = "timeZone", expression = "java(getVenueTimeZone(venueDTO))")
    public abstract BookingListResponse toBookingListResponse(BookingEntity booking, AddressDTO addressDTO, boolean hasEntryPasses, VenueDTO venueDTO);


    public String getVenueTimeZone(VenueDTO venueDTO) {
        return venueDTO.timeZone();
    }

    @AfterMapping
    protected void setEventAttributes(BookingEntity booking, @MappingTarget BookingListResponse.BookingListResponseBuilder response) {
        if (booking == null || booking.getEvent() == null || Collections.isEmpty(booking.getEvent().getEventAttributes())) {
            return;
        }

        for (EventAttributeEntity attribute : booking.getEvent().getEventAttributes()) {
            if (TITLE.equals(attribute.getAttribute().getName())) {
                response.eventTitle(attribute.getAttributeValue());
            }
            if (BANNER.equals(attribute.getAttribute().getName())) {
                response.eventBanner(attribute.getAttributeValue());
            }
        }
    }
}