package com.xo.backend.mappers;

import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.model.dto.EntryPassDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import static com.xo.backend.utlis.Constants.*;

@Mapper(componentModel = "spring")
public interface EntryPassMapper {

    @Mapping(source = "id", target = "entryPassId")
    @Mapping(source = "entryPassEntity.booking.ownerName", target = "bookedByFullName")
    @Mapping(source = "referenceNumber", target = "entryPassReferenceNumber")
    @Mapping(source = "price", target = "shownPrice")
    @Mapping(source = "used", target = "used")
    //Booking tier attributes
    @Mapping(target = "bookingTierName", ignore = true)
    @Mapping(target = "entryPassDescription", ignore = true)
    @Mapping(target = "entryPassIncludedConsumptionAmount", ignore = true)
    @Mapping(target = "entryPassIncludedConsumptionText", ignore = true)
    //Not relevant for admin UI
    @Mapping(target = "qrCode", ignore = true)
    @Mapping(target = "venueName", ignore = true)
    @Mapping(target = "eventName", ignore = true)
    @Mapping(target = "eventStartTime", ignore = true)
    @Mapping(target = "bookingNumber", ignore = true)
    EntryPassDTO toEntryPassDTO(EntryPassEntity entryPassEntity);

    @AfterMapping
    @SuppressWarnings("java:S131")
    default void mapAttributes(EntryPassEntity entryPassEntity, @MappingTarget EntryPassDTO.EntryPassDTOBuilder entryPassDTOBuilder) {
        entryPassEntity.getBookingTier().getBookingTierAttributes().forEach(bookingTierAttributeEntity -> {
            switch (bookingTierAttributeEntity.getAttribute().getName()) {
                case TIER_NAME -> entryPassDTOBuilder.bookingTierName(bookingTierAttributeEntity.getAttributeValue());
                case DESCRIPTION ->
                        entryPassDTOBuilder.entryPassDescription(bookingTierAttributeEntity.getAttributeValue());
                case INCLUDED_CONSUMPTION_AMOUNT ->
                        entryPassDTOBuilder.entryPassIncludedConsumptionAmount(bookingTierAttributeEntity.getAttributeValue());
                case INCLUDED_CONSUMPTION_DESCRIPTION ->
                        entryPassDTOBuilder.entryPassIncludedConsumptionText(bookingTierAttributeEntity.getAttributeValue());
            }
        });
    }
}
