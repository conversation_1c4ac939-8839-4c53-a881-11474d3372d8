package com.xo.backend.mappers;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingOccasion;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.repository.entry_pass.EntryPassCount;
import com.xo.backend.database.repository.entry_pass.EntryPassRepository;
import com.xo.backend.mappers.message.BookingMessageMapper;
import com.xo.backend.model.dto.AdminDetailedBookingDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.utlis.Constants;
import io.jsonwebtoken.lang.Collections;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.support.DefaultStateContext;

import java.util.Optional;

import static com.xo.backend.service.BookingStateMachineConfig.BOOKING_MESSAGE;

@Mapper(componentModel = "spring", uses = {MapperUtils.class, BookingItemMapper.class})
public abstract class AdminDetailedBookingMapper {

    @Autowired
    private  BookingMessageMapper bookingMessageMapper;

    @Autowired
    private EntryPassRepository entryPassRepository;

    @Autowired
    private EntryPassMapper entryPassMapper;

    @Mapping(target = "eventBanner", ignore = true)
    @Mapping(target = "eventTitle", ignore = true)
    @Mapping(target = "entryPasses", ignore = true)
    @Mapping(target = "notes", ignore = true)
    @Mapping(target = "validBookingStatusActions", ignore = true)
    @Mapping(target = "eventId", source = "bookingEntity.eventId")
    @Mapping(target = "createdBy", source = "bookingEntity.createdBy")
    @Mapping(target = "arrivalCounter", source = "entryPassCount.usedCount")
    @Mapping(target = "numberOfEntryPasses", source = "entryPassCount.totalCount")
    @Mapping(target = "bookingChannel", source = "bookingEntity.bookingChannel")
    @Mapping(target = "ownerId", source = "bookingEntity.ownerId")
    @Mapping(target = "bookingOccasion" , expression = "java(getBookingOccasionDisplayName(bookingEntity))")
    @Mapping(target = "arrivalTime", source = "bookingEntity.arrivalTime", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "submittedAt", source = "bookingEntity.submittedAt", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "eventDateTime", source = "bookingEntity.event.startDatetime", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "timeZone", expression = "java(getVenueTimeZone(venueDTO))")
    public abstract AdminDetailedBookingDTO mapToDetailedBookingDTO(BookingEntity bookingEntity, EntryPassCount entryPassCount, @Context StateMachine<BookingStatus, BookingStatus> stateMachine,@Context VenueDTO venueDTO); //this is only used for filter currently

    public String getVenueTimeZone(VenueDTO venueDTO) {
        return venueDTO.timeZone();
    }

    public String getBookingOccasionDisplayName(BookingEntity bookingEntity) {
        return Optional.ofNullable(bookingEntity.getBookingOccasion())
                .map(BookingOccasion::getDisplayValue)
                .orElse(null);
    }

    @AfterMapping
    protected void applyAttributes(BookingEntity bookingEntity, @Context StateMachine<BookingStatus, BookingStatus> stateMachine,
                                 @MappingTarget AdminDetailedBookingDTO.AdminDetailedBookingDTOBuilder adminDetailedBookingDTOBuilder) {

        adminDetailedBookingDTOBuilder.validBookingStatusActions(
                stateMachine.getTransitions().stream()
                        .filter(transition -> {
                            // Check if the source state matches the booking status
                            boolean sourceMatches = transition.getSource().getId().equals(bookingEntity.getStatus());
                            // Check and evaluate the guard
                            boolean guardAllows = true;
                            if (transition.getGuard() != null) {
                                StateContext<BookingStatus, BookingStatus> context = new DefaultStateContext<>(
                                        StateContext.Stage.TRANSITION, // Stage of the state context
                                        MessageBuilder.withPayload(transition.getTrigger().getEvent())
                                                .setHeader(BOOKING_MESSAGE, bookingMessageMapper.getBookingMessage(bookingEntity))
                                                .build(), // Message payload
                                        null,
                                        stateMachine.getExtendedState(), // Extended state
                                        transition, // Current transition
                                        stateMachine, // Current state machine
                                        transition.getSource(), // Source state
                                        transition.getTarget(), // Target state
                                        null // Exception (null as this is not an error context)
                                );

                                guardAllows = Boolean.TRUE.equals(transition.getGuard().apply(context).block());
                            }

                            return sourceMatches && guardAllows;
                        })
                        .map(transition -> transition.getTrigger().getEvent())
                        .toList()
        );


        if (bookingEntity == null || bookingEntity.getEvent() == null) {
            return;
        }

        adminDetailedBookingDTOBuilder.entryPasses(entryPassRepository.findByBooking_Id(bookingEntity.getId()).stream().map(entryPassMapper::toEntryPassDTO).toList());

        if (!Collections.isEmpty(bookingEntity.getEvent().getEventAttributes())) {
            for (EventAttributeEntity eventAttributeEntity : bookingEntity.getEvent().getEventAttributes()) {
                if (Constants.TITLE.equals(eventAttributeEntity.getAttribute().getName())) {
                    adminDetailedBookingDTOBuilder.eventTitle(eventAttributeEntity.getAttributeValue());
                }
                if (Constants.BANNER.equals(eventAttributeEntity.getAttribute().getName())) {
                    adminDetailedBookingDTOBuilder.eventBanner(eventAttributeEntity.getAttributeValue());
                }
            }
        }

            adminDetailedBookingDTOBuilder.notes(bookingEntity.getNotes());
    }
}
