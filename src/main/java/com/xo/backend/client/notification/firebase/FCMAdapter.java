package com.xo.backend.client.notification.firebase;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import com.xo.backend.client.notification.PushNotificationAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ConditionalOnProperty(prefix = "app.clients.notifications", name = "provider", havingValue = "fcm")
public class FCMAdapter implements PushNotificationAdapter {

    @Async
    public void send(String title, String body, String token) {
        try {
            Message message = Message.builder().setToken(token)
                    .setNotification(Notification.builder()
                            .setTitle(title)
                            .setBody(body)
                            .build()
                    )
                    .build();

            FirebaseMessaging.getInstance().send(message);
        } catch (Exception e) {
            log.error("Error sending notification: {}", e.getMessage());
        }
    }
}
