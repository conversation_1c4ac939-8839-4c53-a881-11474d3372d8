package com.xo.backend.client.notification;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.entity.notifications.DeviceTokenEntity;
import com.xo.backend.database.repository.notifications.DeviceTokenRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.xo.backend.client.notification.NotificationTemplate.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationService {

    private static final String EVENT_NAME = "eventName";

    private final PushNotificationAdapter pushNotificationAdapter;
    private final DeviceTokenRepository deviceTokenRepository;

    public void sendNotifications(BookingEntity booking){
        switch (booking.getStatus()) {
            case APPROVED -> sendReservationAccepted(getDeviceTokens(booking), getEventTitle(booking));
            case DECLINED -> sendReservationDeclined(getDeviceTokens(booking), getEventTitle(booking));
            default -> log.info("No notification to send");
        }
    }

    private void sendReservationAccepted(List<String> deviceToken, String eventName) {
        Map<String, Object> data = new HashMap<>();
        data.put(EVENT_NAME, eventName);
        for(String token : deviceToken) {
            processAndSendNotificationTemplate(token, RESERVATION_ACCEPTED, data);
        }
    }

    private void sendReservationDeclined(List<String> deviceToken, String eventName) {
        Map<String, Object> data = new HashMap<>();
        data.put(EVENT_NAME, eventName);
        for(String token : deviceToken) {
            processAndSendNotificationTemplate(token, RESERVATION_DECLINED, data);
        }
    }

    private String getEventTitle(BookingEntity booking) {
        return booking.getEvent().getEventAttributes().stream()
                .filter(e -> e.getAttribute().getName().equals("title"))
                .findFirst()
                .map(EventAttributeEntity::getAttributeValue)
                .orElse("");
    }

    private List<String> getDeviceTokens(BookingEntity booking) {
        return deviceTokenRepository.findByUserId(booking.getOwnerId())
                .stream()
                .map(DeviceTokenEntity::getDeviceToken)
                .toList();
    }

    private void processAndSendNotificationTemplate(String deviceToken, NotificationTemplate template, Map<String, Object> data) {
        String title = template.getTitle();
        String body = template.processBody(data);
        pushNotificationAdapter.send(title, body, deviceToken);
    }
}
