package com.xo.backend.client.notification;

import lombok.Getter;

import java.util.Map;

@Getter
public enum NotificationTemplate {
    RESERVATION_ACCEPTED(
            "Reservation Request Accepted",
            "Your reservation request for {eventName} has been accepted! " +
                    "Complete payment within the next 24 hours to confirm your booking, either via the app or the email we've sent you."
    ),

    RESERVATION_DECLINED(
            "Reservation Request Declined",
            "Unfortunately, your reservation request for {eventName} was declined. " +
                    "For more information, please contact the venue or event organizer directly."
    ),

    RESERVATION_CONFIRMED_PAYMENT(
            "Reservation Confirmed",
            "Your reservation for {eventName} has been confirmed! " +
                    "For details, check the 'Bookings' section and your email."
    ),

    RESERVATION_CONFIRMED_ADMIN(
            "Reservation Confirmed",
            "Your reservation for {eventName} has been confirmed by the venue or event organizer! " +
                    "For details, check the 'Bookings' section and your email."
    ),

    EVENT_CANCELLED(
            "Event Cancelled",
            "We're sorry to inform you that {eventName} has been cancelled. " +
                    "The venue or event organizer will contact you with further instructions. We apologize for the inconvenience."
    ),

    PAYMENT_EXPIRED(
            "Payment Link Expired",
            "Your reservation payment link has expired, and the booking could not be confirmed. " +
                    "If you still wish to attend {eventName}, please create a new booking or contact the venue or event organizer directly."
    );

    private final String title;
    private final String body;

    NotificationTemplate(String title, String body) {
        this.title = title;
        this.body = body;
    }

    public String processBody(Map<String, Object> data) {
        String processedBody = this.body;
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            processedBody = processedBody.replace("{" + entry.getKey() + "}",
                    entry.getValue() != null ? entry.getValue().toString() : "");
        }
        return processedBody;
    }
}
