package com.xo.backend.client.notification.firebase;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.ByteArrayInputStream;
import java.io.IOException;

@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.clients.notifications", name = "provider", havingValue = "fcm")
public class FirebaseConfig {
    private final FirebaseProperties firebaseProperties;

    @Bean
    public FirebaseApp firebaseApp() {
        try {
            log.info("Initializing Firebase with credentials: {}...", firebaseProperties.getCredentialsB64().substring(0, 10));

            byte[] decodedCredentials = java.util.Base64.getDecoder()
                    .decode(firebaseProperties.getCredentialsB64());
            
            try (ByteArrayInputStream serviceAccount = new ByteArrayInputStream(decodedCredentials)) {
                FirebaseOptions options = FirebaseOptions.builder()
                        .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                        .build();

                if (FirebaseApp.getApps().isEmpty()) {
                    return FirebaseApp.initializeApp(options);
                } else {
                    return FirebaseApp.getInstance();
                }
            }
        } catch (IllegalArgumentException e) {
            log.error("Failed to decode Firebase credentials", e);
            throw new IllegalStateException("Invalid Base64 Firebase credentials", e);
        } catch (IOException e) {
            log.error("Failed to initialize Firebase", e);
            throw new IllegalStateException("Firebase initialization failed", e);
        }
    }
}
