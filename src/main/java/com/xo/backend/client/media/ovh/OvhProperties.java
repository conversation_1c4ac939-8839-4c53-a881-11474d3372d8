package com.xo.backend.client.media.ovh;

import com.xo.backend.client.media.S3CloudProperties;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "spring.cloud.ovh.object-store")
public class OvhProperties implements S3CloudProperties {
    private String endpoint;
    private String bucket;
    private String region;
    private String accessKey;
    private String secretKey;

    public String getPublicEndpoint() {
        return endpoint.replace("https://", StringUtils.join("https://",bucket,"."));
    }
}
