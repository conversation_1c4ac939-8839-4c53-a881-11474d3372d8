package com.xo.backend.client.media.contabo;

import com.xo.backend.client.media.S3CloudProperties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "spring.cloud.contabo.object-store")
public class ContaboProperties implements S3CloudProperties {
    private String endpoint;
    private String bucketPrefix;
    private String bucket;
    private String region;
    private String accessKey;
    private String secretKey;

    public String getBucketDetails() {
        return bucketPrefix + ":" + bucket;
    }
}
