package com.xo.backend.client.media.ovh;

import com.xo.backend.client.media.AbstractS3Adapter;
import com.xo.backend.client.media.S3Utils;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URISyntaxException;

@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.clients.media.provider",havingValue = "ovh")
public class OvhAdapter extends AbstractS3Adapter {

    private final OvhProperties ovhProperties;

    public String processUploadReturnValue(String fileName) {


        return String.join("/",ovhProperties.getPublicEndpoint(),fileName); //reroute to mock s3
    }

    @Override
    protected S3Client initializeS3Client() throws URISyntaxException {
        return S3Utils.createS3Client(ovhProperties);
    }

    @Override
    protected String getBucketName() {
        return ovhProperties.getBucket();
    }

}
