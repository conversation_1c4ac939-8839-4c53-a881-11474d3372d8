package com.xo.backend.client.media;

import lombok.experimental.UtilityClass;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URI;
import java.net.URISyntaxException;

@UtilityClass
public class S3Utils {

    public S3Client createS3Client(S3CloudProperties s3CloudProperties) throws URISyntaxException {
        Region region = Region.of(s3CloudProperties.getRegion());
        URI endpoint = new URI(s3CloudProperties.getEndpoint());

        AwsCredentialsProvider credentialsProvider=StaticCredentialsProvider.create(
                AwsBasicCredentials.create(s3CloudProperties.getAccessKey(), s3CloudProperties.getSecretKey()));
        return S3Client.builder()
                .credentialsProvider(credentialsProvider)
                .endpointOverride(endpoint)
                .forcePathStyle(true)
                .region(region)
                .build();
    }
}
