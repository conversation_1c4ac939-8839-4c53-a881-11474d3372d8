package com.xo.backend.client.media.s3mock;

import com.xo.backend.client.media.AbstractS3Adapter;
import com.xo.backend.client.media.S3Utils;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URISyntaxException;

@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.clients.media.provider", havingValue = "s3mock")
public class S3MockAdapter extends AbstractS3Adapter {

    private final S3MockProperties s3MockProperties;

    public String processUploadReturnValue(String fileName) {

        return String.join("/", s3MockProperties.getEndpoint(), s3MockProperties.getBucket(), fileName);
    }

    @Override
    protected S3Client initializeS3Client() throws URISyntaxException {
        return S3Utils.createS3Client(s3MockProperties);
    }

    @Override
    protected String getBucketName() {
        return s3MockProperties.getBucket();
    }
}
