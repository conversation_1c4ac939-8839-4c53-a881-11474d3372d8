package com.xo.backend.client.media.contabo;

import com.xo.backend.client.media.AbstractS3Adapter;
import com.xo.backend.client.media.S3Utils;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URISyntaxException;

@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.clients.media.provider",havingValue = "contabo")
public class ContaboAdapter extends AbstractS3Adapter {

    private final ContaboProperties contaboProperties;

    public String processUploadReturnValue(String fileName) {


            return contaboProperties.getEndpoint() + "/" + contaboProperties.getBucketDetails() +"/" + fileName;
    }

    @Override
    protected S3Client initializeS3Client() throws URISyntaxException {
        return S3Utils.createS3Client(contaboProperties);
    }

    @Override
    protected String getBucketName() {
        return contaboProperties.getBucket();
    }


}