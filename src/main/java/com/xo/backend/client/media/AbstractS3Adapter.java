package com.xo.backend.client.media;

import com.xo.backend.error.exceptions.FileUploadException;
import com.xo.backend.error.exceptions.GeneralException;
import com.xo.backend.model.UploadFileType;
import com.xo.backend.utlis.ImageUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.ObjectCannedACL;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import javax.imageio.ImageIO;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.util.Optional;
import java.util.UUID;

public abstract class AbstractS3Adapter implements MediaAdapter {

    // Lazily initialized S3Client
    private S3Client s3Client;

    protected S3Client getS3Client() throws URISyntaxException {
        if (s3Client == null) {
            s3Client = initializeS3Client();
        }
        return s3Client;
    }

    public String uploadFile(MultipartFile file, UploadFileType uploadFileType) {


        if (!uploadFileType.getAcceptableFileTypes().contains(file.getContentType())) {
            throw new FileUploadException("File type: " + uploadFileType.name() + " doesn't match: " + file.getContentType() + ". Valid formats are: "
                    + uploadFileType.getAcceptableFileTypes());
        }

        String formatName = ImageUtils.getFileExtension(file.getContentType());
        String fileName = UUID.randomUUID() + formatName;
        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(getBucketName())
                .key(fileName)
                .acl(ObjectCannedACL.PUBLIC_READ)
                .contentType(file.getContentType())
                .build();
        try {
            InputStream inputStream;
            if (uploadFileType.isCompressionRequired() && file.getSize() > uploadFileType.getRecommendedSizeInBytes()) {

                inputStream = Optional.ofNullable(ImageUtils.compressFile(ImageIO.read(file.getInputStream()), formatName, file.getSize(), uploadFileType.getRecommendedImageWidth(), uploadFileType.getRecommendedImageHeight())).orElse(file.getInputStream());

            } else {
                inputStream = file.getInputStream();
            }

            try (inputStream) {
                getS3Client().putObject(request, RequestBody.fromInputStream(inputStream, inputStream.available()));
            }

        } catch (Exception e) {
            throw new GeneralException("Failed to upload file: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return processUploadReturnValue(fileName);
    }

    public InputStreamResource downloadFile(String fileName) {
        try {
            GetObjectRequest request = GetObjectRequest.builder()
                    .bucket(getBucketName())
                    .key(fileName)
                    .build();
            return new InputStreamResource(getS3Client().getObject(request));
        } catch (Exception e) {
            throw new GeneralException("Failed to download file: " + fileName, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public void deleteFile(String fileName) {
        try {
            DeleteObjectRequest request = DeleteObjectRequest.builder()
                    .bucket(getBucketName())
                    .key(fileName)
                    .build();
            getS3Client().deleteObject(request);
        }catch (Exception e) {
            throw new GeneralException("Failed to delete file: " + fileName, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Abstract method for the provider-specific logic (custom return values or handling)
    protected abstract String processUploadReturnValue(String fileName);

    // Abstract method for subclasses to provide S3Client
    protected abstract S3Client initializeS3Client() throws URISyntaxException;

    protected abstract String getBucketName();

}
