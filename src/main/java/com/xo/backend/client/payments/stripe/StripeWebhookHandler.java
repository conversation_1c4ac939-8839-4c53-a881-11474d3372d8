package com.xo.backend.client.payments.stripe;

import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.Event;
import com.stripe.model.StripeObject;
import com.stripe.model.checkout.Session;
import com.stripe.net.Webhook;
import com.xo.backend.client.email.EmailService;
import com.xo.backend.client.payments.PaymentProvider;
import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.entity.bookings.PaymentStatus;
import com.xo.backend.database.entity.payments.PaymentTransactionEntity;
import com.xo.backend.database.entity.payments.TransactionStatus;
import com.xo.backend.database.entity.payments.WebhookLogEntity;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.repository.payments.PaymentTransactionRepository;
import com.xo.backend.database.repository.payments.WebhookLogRepository;
import com.xo.backend.error.exceptions.GeneralException;
import com.xo.backend.service.AdminBookingTierService;
import com.xo.backend.service.EntryPassService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

import static com.xo.backend.client.payments.StripeWebhookEventType.CHECKOUT_SESSION_COMPLETED;
import static com.xo.backend.client.payments.StripeWebhookEventType.CHECKOUT_SESSION_EXPIRED;

@Component
@Slf4j
@RequiredArgsConstructor
public class StripeWebhookHandler {

    private final StripeConfig stripeConfig;
    private final WebhookLogRepository webhookLogRepository;
    private final PaymentTransactionRepository paymentTransactionRepository;
    private final EntryPassService entryPassService;
    private final AdminBookingTierService adminBookingTierService;
    private final EmailService emailService;
    private final BookingRepository bookingRepository;

    @Transactional
    public void handleStripeConnectWebhook(String payload, String signature) {

        String webhookSecret = stripeConfig.getConnectEndpointSecret();
        try {
            Event event = Webhook.constructEvent(payload, signature, webhookSecret);

            if (webhookLogRepository.existsById(event.getId())) {
                log.info("Webhook with id {} already processed", event.getId());
                return;
            }

            Optional<StripeObject> stripeObject = event.getDataObjectDeserializer().getObject();
            if (stripeObject.isPresent()) {
                switch (event.getType()) {
                    case CHECKOUT_SESSION_COMPLETED -> handleCheckoutSessionCompleted((Session) stripeObject.get());
                    case CHECKOUT_SESSION_EXPIRED -> handleCheckoutSessionExpired((Session) stripeObject.get());
                    default -> log.info("Unsupported webhook event type: {}", event.getType());
                }
                createWebhookLog(event);
            }

        } catch (SignatureVerificationException e) {
            throw new IllegalArgumentException("Invalid webhook signature");
        } catch (Exception e) {
            log.error("Error handling webhook: {}", e.getMessage());
            throw new GeneralException("Error handling webhook: " + e.getMessage());
        }
    }

    private void createWebhookLog(Event event) {

        WebhookLogEntity webhookLogEntity = WebhookLogEntity.builder()
                .id(event.getId())
                .provider(PaymentProvider.STRIPE)
                .eventType(event.getType())
                .receivedAt(Instant.now())
                .build();

        webhookLogRepository.save(webhookLogEntity);
    }

    private void handleCheckoutSessionCompleted(Session session) {
        log.info("Processing checkout.session.completed for session: {}", session.getId());

        Optional<PaymentTransactionEntity> transactionOptional = paymentTransactionRepository
                .findByProviderSessionId(session.getId());

        if (transactionOptional.isEmpty()) {
            log.info("Transaction not found for session id: {}", session.getId());
            return;
        }

        PaymentTransactionEntity transaction = transactionOptional.get();

        transaction.setProviderTransactionId(session.getPaymentIntent());
        transaction.setTransactionStatus(TransactionStatus.SUCCESS);
        transaction.setCompletedAt(Instant.now());

        BookingEntity booking = transaction.getBooking();
        booking.setPaymentStatus(PaymentStatus.PAID);
        booking.setStatus(BookingStatus.CONFIRMED);

        if (booking.getSubmittedAt() == null) {
            booking.setSubmittedAt(Instant.now());
        }
        /*No need to update availability here as it's already updated in the creation session flow*/
        entryPassService.createEntryPasses(booking);

        bookingRepository.save(booking);
        paymentTransactionRepository.save(transaction);
        log.info("Successfully processed payment for session: {}", session.getId());

        emailService.sendBookingEmail(booking);
    }

    private void handleCheckoutSessionExpired(Session session) {
        log.info("Processing checkout.session.expired for session: {}", session.getId());

        Optional<PaymentTransactionEntity> transactionOptional = paymentTransactionRepository
                .findByProviderSessionId(session.getId());

        if (transactionOptional.isEmpty()) {
            log.info("Transaction not found for session id: {}", session.getId());
            return;
        }

        PaymentTransactionEntity transaction = transactionOptional.get();
        TransactionStatus status = determineTransactionStatus(session);
        String failureReason = determineFailureReason(session, status);

        transaction.setProviderTransactionId(session.getPaymentIntent());
        transaction.setTransactionStatus(status);
        transaction.setFailureReason(failureReason);
        transaction.setCompletedAt(Instant.now());
        BookingEntity booking = transaction.getBooking();
        if(booking.getStatus() == BookingStatus.APPROVED) {
            booking.setStatus(BookingStatus.CANCELLED);
        }
        /*Revert availability*/
        adminBookingTierService.revertBookingTiersAvailabilities(booking);

        paymentTransactionRepository.save(transaction);
        log.info("Processed expired session: {}. Status: {}, Reason: {}", session.getId(), status, failureReason);
    }

    private TransactionStatus determineTransactionStatus(Session session) {
        if ("expired".equals(session.getStatus())) {
            // Check if payment was even attempted
            return session.getPaymentIntent() == null ? TransactionStatus.EXPIRED : TransactionStatus.FAILED;
        }
        return TransactionStatus.FAILED;
    }

    private String determineFailureReason(Session session, TransactionStatus status) {
        return switch (status) {
            case EXPIRED -> {
                if (session.getCustomerDetails() == null) {
                    yield "Session expired - customer never accessed checkout";
                }
                yield "Session expired - customer abandoned checkout";
            }
            case FAILED -> {
                if ("unpaid".equals(session.getPaymentStatus())) {
                    yield "Payment was initiated but not completed successfully";
                }
                yield "Payment failed - " + session.getPaymentStatus();
            }
            default -> "Unknown failure reason";
        };
    }

}
