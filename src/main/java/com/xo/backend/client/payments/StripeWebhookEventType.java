package com.xo.backend.client.payments;

import lombok.experimental.UtilityClass;

@UtilityClass
public class StripeWebhookEventType {
    public static final String CHECKOUT_SESSION_COMPLETED = "checkout.session.completed";
    public static final String CHECKOUT_SESSION_EXPIRED = "checkout.session.expired";
    public static final String PAYMENT_INTENT_FAILED = "payment_intent.payment_failed";
    public static final String PAYMENT_INTENT_CANCELED = "payment_intent.canceled";
    public static final String PAYMENT_INTENT_SUCCEEDED = "payment_intent.succeeded";
}
