package com.xo.backend.client.payments;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.payments.PaymentTransactionEntity;
import com.xo.backend.model.dto.responses.payments.CreateCheckoutResponse;

public interface PaymentAdapter {
    CreateCheckoutResponse createCheckoutSession(BookingEntity bookingEntity);
    void expireCheckoutSession(PaymentTransactionEntity paymentTransactionEntity);

    boolean isEnabled();

    PaymentProvider getProvider();
    String generatePaymentLinkForEmail(BookingEntity bookingEntity);
}