package com.xo.backend.client.payments;


import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Currency;

@Getter
@RequiredArgsConstructor
public enum SupportedCurrency {
    USD(Currency.getInstance("USD")),
    EUR(Currency.getInstance("EUR")),
    GBP(Currency.getInstance("GBP"));

    private final Currency currency;

    public long toMinorUnits(BigDecimal amount) {
        return amount
                .multiply(BigDecimal.TEN.pow(currency.getDefaultFractionDigits()))
                .setScale(0, RoundingMode.HALF_UP)
                .longValue();
    }
}
