package com.xo.backend.client.payments.stripe;

import com.stripe.Stripe;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Getter
@Setter
@ConfigurationProperties(prefix = "app.clients.payment.stripe")
public class StripeConfig {

    private String secretKey;
    private String connectEndpointSecret;
    private boolean isEnabled;
    private String successUrl;
    private String successPaymentLinkUrl;
    private String cancelUrl;
    private String cancelPaymentLinkUrl;
    private Integer checkoutExpirationMinutes;
    private Integer payByLinkExpirationHours;

    @PostConstruct
    @SuppressWarnings("java:S2696")
    public void init() {
        Stripe.apiKey = secretKey;
    }
}
