package com.xo.backend.client.payments.stripe;

import com.stripe.exception.StripeException;
import com.stripe.model.checkout.Session;
import com.stripe.net.RequestOptions;
import com.stripe.param.checkout.SessionCreateParams;
import com.xo.backend.client.payments.PaymentAdapter;
import com.xo.backend.client.payments.PaymentProvider;
import com.xo.backend.client.payments.SupportedCurrency;
import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.payments.PaymentTransactionEntity;
import com.xo.backend.database.entity.payments.TransactionStatus;
import com.xo.backend.database.repository.payments.PaymentTransactionRepository;
import com.xo.backend.error.exceptions.GeneralException;
import com.xo.backend.error.exceptions.InvalidCurrencyException;
import com.xo.backend.error.exceptions.PaymentRequestException;
import com.xo.backend.error.exceptions.StripeSessionExpirationException;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.responses.payments.CreateCheckoutResponse;
import com.xo.backend.service.AdminBookingTierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class StripeAdapter implements PaymentAdapter {
    private final StripeConfig stripeConfig;
    private final PaymentTransactionRepository paymentTransactionRepository;
    private final AdminBookingTierService adminBookingTierService;
    private final VenueInfoService venueInfoService;

    @Override
    public CreateCheckoutResponse createCheckoutSession(BookingEntity bookingEntity) {
        try {
            String successUrl = replaceBookingId(stripeConfig.getSuccessUrl(), bookingEntity.getId());
            String failureUrl = replaceBookingId(stripeConfig.getCancelUrl(), bookingEntity.getId());
            Session session = getOrCreatePendingStripeSession(bookingEntity,
                    stripeConfig.getCheckoutExpirationMinutes(), successUrl, failureUrl);
            return CreateCheckoutResponse.builder()
                    .checkoutUrl(session.getUrl())
                    .timestamp(Instant.now())
                    .sessionExpirationTimestamp(Instant.ofEpochSecond(session.getExpiresAt()))
                    .build();
        } catch (Exception e) {
            log.error("Error while creating checkout session: {}", e.getMessage());
            throw new PaymentRequestException("Error while creating checkout session: " + e.getMessage());
        }
    }

    private Session getOrCreatePendingStripeSession(BookingEntity bookingEntity, long expirationMinutes,
                                                    String successUrl, String failureUrl) throws StripeException {
        Session session;
        /*Retrieve the existing session if it's still pending , otherwise create new one*/
        Optional<PaymentTransactionEntity> paymentTransactionEntityOptional =
                paymentTransactionRepository.findByBookingAndTransactionStatus(bookingEntity,
                        TransactionStatus.PENDING);
        if (paymentTransactionEntityOptional.isPresent()) {
            session = retrieveConnectedAccountStripeSession(paymentTransactionEntityOptional.get());
        } else {
            /*check and update booking tier availability*/
            adminBookingTierService.updateBookingAvailability(bookingEntity);
            VenueDTO venueDTO = venueInfoService.getVenueDetails(bookingEntity.getEvent().getVenueId());
            session = createStripeSession(bookingEntity, venueDTO, expirationMinutes,
                    successUrl, failureUrl);
            String connectedAccountId = Optional.ofNullable(venueDTO.settings().get("STRIPE_ACCOUNT_ID")).orElse("");
            createPendingPaymentTransaction(bookingEntity, session, connectedAccountId);
        }
        return session;
    }

    @Override
    public boolean isEnabled() {
        return stripeConfig.isEnabled();
    }

    @Override
    public PaymentProvider getProvider() {
        return PaymentProvider.STRIPE;
    }

    @Override
    public String generatePaymentLinkForEmail(BookingEntity bookingEntity) {
        try {
            Session session = getOrCreatePendingStripeSession(bookingEntity,
                    (long) stripeConfig.getPayByLinkExpirationHours() * 60, stripeConfig.getSuccessPaymentLinkUrl(),
                    stripeConfig.getCancelPaymentLinkUrl());
            return session.getUrl();
        } catch (StripeException e) {
            log.error("Stripe error while creating session: {}", e.getMessage());
            throw new GeneralException("Error generating Stripe payment link");
        }
    }

    private Session createStripeSession(BookingEntity bookingEntity, VenueDTO venueDTO, long expirationMinutes,
                                        String successUrl,
                                        String cancelUrl) throws StripeException {

        SupportedCurrency currency = Optional.ofNullable(venueDTO.currency().isoCode())
                .map(this::validateCurrency)
                .orElseThrow(() -> new InvalidCurrencyException("Missing currency"));

        String connectedAccountId = Optional.ofNullable(venueDTO.settings().get("STRIPE_ACCOUNT_ID"))
                .orElseThrow(() -> new GeneralException("Missing connected account id"));

        RequestOptions requestOptions = RequestOptions.builder().setStripeAccount(connectedAccountId).build();

        BigDecimal totalAmount = bookingEntity.getTotalAmount();
        BigDecimal platformFee = bookingEntity.getTotalFee();

        SessionCreateParams params = SessionCreateParams.builder()
                .addLineItem(SessionCreateParams.LineItem.builder()
                        .setPriceData(SessionCreateParams.LineItem.PriceData.builder()
                                .setCurrency(currency.getCurrency().getCurrencyCode().toLowerCase())
                                .setUnitAmount(currency.toMinorUnits(totalAmount))
                                .setProductData(SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                        .setName("Payment for " + bookingEntity.getBookingNumber())
                                        .build())
                                .build())
                        .setQuantity(1L)
                        .build())
                .setPaymentIntentData(SessionCreateParams.PaymentIntentData.builder()
                        .setApplicationFeeAmount(currency.toMinorUnits(platformFee))
                        .build())
                .setMode(SessionCreateParams.Mode.PAYMENT)
                .setSuccessUrl(successUrl)
                .setCancelUrl(cancelUrl)
                .setCustomerEmail(bookingEntity.getOwnerEmail())
                .setExpiresAt(Instant.now().plus(expirationMinutes, ChronoUnit.MINUTES).getEpochSecond()).build();

        return Session.create(params, requestOptions);
    }

    private SupportedCurrency validateCurrency(String code) {
        try {
            return SupportedCurrency.valueOf(code);
        } catch (IllegalArgumentException e) {
            throw new InvalidCurrencyException("Unsupported currency: " + code);
        }
    }

    private void createPendingPaymentTransaction(BookingEntity bookingEntity, Session session,
                                                 String connectedAccountId) {
        SupportedCurrency currency = SupportedCurrency.valueOf(session.getCurrency().toUpperCase());
        BigDecimal platformFee = bookingEntity.getTotalFee();

        PaymentTransactionEntity paymentTransactionEntity = PaymentTransactionEntity.builder()
                .booking(bookingEntity)
                .totalAmount(bookingEntity.getTotalAmount())
                .paymentProvider(PaymentProvider.STRIPE)
                .transactionStatus(TransactionStatus.PENDING)
                .currency(currency)
                .providerSessionId(session.getId())
                .platformFee(platformFee)
                .userId(bookingEntity.getOwnerId())
                .connectedAccountId(connectedAccountId)
                .build();

        paymentTransactionRepository.save(paymentTransactionEntity);
    }

    private Session retrieveConnectedAccountStripeSession(PaymentTransactionEntity paymentTransactionEntity) throws StripeException {
        RequestOptions requestOptions =
                RequestOptions.builder().setStripeAccount(paymentTransactionEntity.getConnectedAccountId()).build();
        return Session.retrieve(paymentTransactionEntity.getProviderSessionId(), requestOptions);
    }

    public void expireCheckoutSession(PaymentTransactionEntity paymentTransactionEntity) {
        RequestOptions requestOptions =
                RequestOptions.builder().setStripeAccount(paymentTransactionEntity.getConnectedAccountId()).build();
        try {
            Session session = Session.retrieve(paymentTransactionEntity.getProviderSessionId(), requestOptions);

            if (session == null) {
                log.debug("Session not found : {}", paymentTransactionEntity.getProviderSessionId());
                paymentTransactionEntity.setTransactionStatus(TransactionStatus.EXPIRED);
                paymentTransactionRepository.save(paymentTransactionEntity);
                return;
            }

            switch (session.getStatus()) {
                case "expired" -> {
                    log.debug("Session already expired : {}", paymentTransactionEntity.getProviderSessionId());
                    paymentTransactionEntity.setTransactionStatus(TransactionStatus.EXPIRED);
                    paymentTransactionRepository.save(paymentTransactionEntity);
                }
                case "complete" -> log.debug("Session already completed : {}", paymentTransactionEntity.getProviderSessionId());
                default -> {
                    log.debug("Expiring checkout session: {}", paymentTransactionEntity.getProviderSessionId());
                    session.expire(requestOptions);
                }
            }
        } catch (StripeException e) {
            log.error("Error expiring checkout session: {} due to {}", paymentTransactionEntity.getProviderSessionId(),
                    e.getMessage());
            throw new StripeSessionExpirationException("Failed to expire checkout session");
        }
    }

    private String replaceBookingId(String url, Integer bookingId) {
        return url.replace("{bookingId}", String.valueOf(bookingId));
    }
}
