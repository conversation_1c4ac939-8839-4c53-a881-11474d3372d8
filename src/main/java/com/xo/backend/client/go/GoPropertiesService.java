package com.xo.backend.client.go;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class GoPropertiesService {

    private final GoProperties goProperties;

    public String getCurrentUserUrl() {
        return goProperties.getBasePath() + goProperties.getCurrentUser();
    }

    public String getUserProfileUrl() {
        return goProperties.getBasePath() + goProperties.getUserProfile();
    }

    public String getVenueDetailsUrl(String venueId) {
        return goProperties.getBasePath() + goProperties.getVenueDetails() + venueId;
    }

    public String getApiKey() {
        return goProperties.getApiKey();
    }

    public String getVenueOverview(){
        return goProperties.getBasePath() +goProperties.getVenueOverview();
    }
}
