package com.xo.backend.client.go;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;


@Getter
@Setter
@ConfigurationProperties(prefix = "app.clients.go")
public class GoProperties {

    private int timeout;
    private String basePath;
    private String apiKey;

    //endpoints
    @Value("${app.clients.go.endpoints.current-user}")
    private String currentUser;

    @Value("${app.clients.go.endpoints.venue-details}")
    private String venueDetails;

    @Value("${app.clients.go.endpoints.venue-overview}")
    private String venueOverview;

    @Value("${app.clients.go.endpoints.user-profile}")
    private String userProfile;
}
