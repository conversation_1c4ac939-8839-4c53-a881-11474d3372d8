package com.xo.backend.client.go;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

@Component
@RequiredArgsConstructor
public class GoRequestBuilder {

    private final GoPropertiesService propertiesService;

    public HttpEntity<MultiValueMap<String, String>> buildHttpEntityWithToken(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.AUTHORIZATION, token);
        return new HttpEntity<>(headers);
    }

    public HttpEntity<MultiValueMap<String, String>> buildHttpEntityForWithApiKey() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("x-api-key", propertiesService.getApiKey());
        return new HttpEntity<>(headers);
    }

}
