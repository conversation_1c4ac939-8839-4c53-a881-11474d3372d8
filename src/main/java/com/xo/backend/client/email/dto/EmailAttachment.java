package com.xo.backend.client.email.dto;

import java.util.Arrays;
import java.util.Objects;

public record EmailAttachment(
        byte[] content,
        String fileName,
        String contentType
) {
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EmailAttachment that = (EmailAttachment) o;
        return Arrays.equals(content, that.content)
                && Objects.equals(fileName, that.fileName)
                && Objects.equals(contentType, that.contentType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(Arrays.hashCode(content), fileName, contentType);
    }

    @Override
    public String toString() {
        return "EmailAttachment[" +
                "content=" + Arrays.toString(content) +
                ", fileName='" + fileName + '\'' +
                ", contentType='" + contentType + '\'' +
                ']';
    }
}
