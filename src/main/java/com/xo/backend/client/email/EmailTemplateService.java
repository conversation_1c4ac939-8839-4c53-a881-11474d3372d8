package com.xo.backend.client.email;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

@Service
@RequiredArgsConstructor
public class EmailTemplateService {

    private final TemplateEngine templateEngine;

    /**
     * Renders a Thymeleaf template with the provided context.
     *
     * @param templateName The name of the Thymeleaf template (e.g., "reservationPending").
     * @param context      The Thymeleaf context containing dynamic data.
     * @return The rendered HTML as a String.
     */
    public String renderTemplate(String templateName, Context context) {
        return templateEngine.process(templateName, context);
    }
}