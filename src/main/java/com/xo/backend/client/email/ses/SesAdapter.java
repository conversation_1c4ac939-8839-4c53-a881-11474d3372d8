package com.xo.backend.client.email.ses;

import com.xo.backend.client.email.EmailAdapter;
import com.xo.backend.client.email.dto.EmailAttachment;
import com.xo.backend.client.email.dto.EmailContent;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.SendRawEmailRequest;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Properties;

import static jakarta.mail.Message.RecipientType.TO;
import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
@Service
@ConditionalOnProperty(name = "app.clients.email.provider", havingValue = "ses")
@RequiredArgsConstructor
public class SesAdapter implements EmailAdapter {

    private final SesClient sesClient;

    @Async("emailExecutor")
    @Override
    public void sendEmail(EmailContent emailContent) {
        try {
            log.info("Starting async email process v2 to {}", emailContent.to());
            SendRawEmailRequest rawEmailRequest = createSendEmailRequest(emailContent);
            sesClient.sendRawEmail(rawEmailRequest);
            log.info("Email successfully sent to {}", emailContent.to());
        } catch (Exception e) {
            log.error("Failed to send email to {}", emailContent.to(), e);
        }
    }

    private SendRawEmailRequest createSendEmailRequest(EmailContent emailContent) throws MessagingException, IOException {
        Session session = Session.getDefaultInstance(new Properties());
        MimeMessage mimeMessage = new MimeMessage(session);

        // Set email subject, sender, and recipient
        mimeMessage.setSubject(emailContent.subject(), UTF_8.name());
        mimeMessage.setFrom(new InternetAddress(emailContent.from(), emailContent.fromName()));
        mimeMessage.setRecipients(TO, InternetAddress.parse(emailContent.to()));
        if(emailContent.replyTo() != null){
            mimeMessage.setReplyTo(InternetAddress.parse(emailContent.replyTo()));
        }
        // Create the email body
        MimeMultipart messageBody = createMultipartMessage(emailContent.body(), emailContent.attachments());
        mimeMessage.setContent(messageBody);

        // Convert the MimeMessage to a byte array
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        mimeMessage.writeTo(outputStream);

        // Build the SES raw email request
        return SendRawEmailRequest.builder()
                .rawMessage(m -> m.data(SdkBytes.fromByteArray(outputStream.toByteArray())))
                .build();
    }

    private MimeMultipart createMultipartMessage(String body, List<EmailAttachment> attachments) throws MessagingException {
        MimeMultipart messageBody = new MimeMultipart("mixed");

        // Add the HTML body part
        MimeBodyPart htmlPart = new MimeBodyPart();
        htmlPart.setContent(body, "text/html; charset=UTF-8");
        messageBody.addBodyPart(htmlPart);

        // Add attachments if any
        if (attachments != null) {
            for (EmailAttachment attachment : attachments) {
                MimeBodyPart attachmentPart = new MimeBodyPart();
                attachmentPart.setFileName(attachment.fileName()); // Set the attachment name
                attachmentPart.setContent(attachment.content(), attachment.contentType()); // Set the attachment data and content type
                messageBody.addBodyPart(attachmentPart);
            }
        }

        return messageBody;
    }
}