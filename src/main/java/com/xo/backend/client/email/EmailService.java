package com.xo.backend.client.email;

import com.google.zxing.WriterException;
import com.xo.backend.client.email.dto.EmailAttachment;
import com.xo.backend.client.email.dto.EmailContent;
import com.xo.backend.client.email.dto.TicketEmailInfo;
import com.xo.backend.client.payments.PaymentAdapter;
import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.repository.entry_pass.EntryPassRepository;
import com.xo.backend.database.service.BookingTierAttributeService;
import com.xo.backend.database.service.EventAttributeService;
import com.xo.backend.model.dto.SupportEmailOptions;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.utlis.SecurityContextUtil;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.xo.backend.utlis.Constants.*;
import static com.xo.backend.utlis.CurrencyFormatter.formatCurrency;
import static com.xo.backend.utlis.TimeFormatter.formatInstantInZone;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final EmailTemplateService templateService;
    private final EmailAdapter emailAdapter;
    private final EventAttributeService eventAttributeService;
    private final BookingTierAttributeService bookingTierAttributeService;
    private final PaymentAdapter paymentAdapter;
    private final EntryPassRepository entryPassRepository;
    private final VenueInfoService venueInfoService;
    private final EmailProperties emailProperties;

    public void sendBookingEmail(BookingEntity booking) {
        if (isInvalidBooking(booking)) return;

        try {
            switch (booking.getStatus()) {
                case PENDING -> sendPendingEmail(booking);
                case APPROVED -> sendApprovedEmail(booking);
                case CONFIRMED -> handleConfirmedBooking(booking);
                case DECLINED -> sendDeclinedEmail(booking);
                default -> log.warn("Unhandled booking status: {}", booking.getStatus());
            }
        } catch (Exception e) {
            log.error("Failed to process email for booking {}", booking.getId(), e);
        }
    }

    public void sendBulkEmails(List<BookingEntity> updatedBookings) {
        for (BookingEntity booking : updatedBookings) {
            sendBookingEmail(booking);
        }
    }

    public void sendSupportEmail(@NotNull SupportEmailOptions subject, @NotNull String message) {

        // Extract user email from authentication token
        String userEmail = SecurityContextUtil.getCurrentUsername();

        // Create a context for Thymeleaf template
        Context context = new Context();
        context.setVariable("subject", subject.getDisplayName());
        context.setVariable("message", message);
        context.setVariable("senderEmail", userEmail);

        // Process the template
        String body = templateService.renderTemplate("inline/emails/support-help-email", context);

        emailAdapter.sendEmail(EmailContent.builder()
                .to(emailProperties.support().recipient())
                .from(emailProperties.support().sender())
                .fromName(emailProperties.support().senderName())
                .replyTo(userEmail)
                .subject(subject.getDisplayName())
                .body(body)
                .build());
    }

    private void sendPendingEmail(BookingEntity booking) {
        Context context = createGenericReservationContext(booking);

        String body = templateService.renderTemplate("inline/emails/pending-reservation-email", context);

        emailAdapter.sendEmail(EmailContent.builder()
                .to(booking.getOwnerEmail())
                .from(emailProperties.transactional().sender())
                .fromName(emailProperties.transactional().senderName())
                .subject("Your Reservation Request is In! Stay Tuned")
                .body(body)
                .build());
    }

    private void sendDeclinedEmail(BookingEntity booking) {
        Context context = createGenericReservationContext(booking);

        String body = templateService.renderTemplate("inline/emails/declined-reservation-email", context);

        emailAdapter.sendEmail(EmailContent.builder()
                .to(booking.getOwnerEmail())
                .from(emailProperties.transactional().sender())
                .fromName(emailProperties.transactional().senderName())
                .subject("Your Reservation Request Has Been Declined")
                .body(body)
                .build());
    }

    private void sendApprovedEmail(BookingEntity booking) {
        Context context = createGenericReservationContext(booking);

        String body = templateService.renderTemplate("inline/emails/accepted-reservation-email", context);

        String pdfBody = templateService.renderTemplate("inline/attachments/accepted-reservation-booking-pdf", context);
        EmailAttachment attachment = PdfService.generateA4Pdf(pdfBody,
                getEventAttributeValue(booking.getEvent(), TITLE) + " - Reservation Request.pdf");

        emailAdapter.sendEmail(EmailContent.builder()
                .to(booking.getOwnerEmail())
                .from(emailProperties.transactional().sender())
                .fromName(emailProperties.transactional().senderName())
                .subject("Your Reservation is Accepted! Complete Payment to Confirm")
                .body(body)
                .attachments(List.of(attachment))
                .build());
    }

    private void handleConfirmedBooking(BookingEntity booking) throws IOException, WriterException {
        switch (booking.getBookingType()) {
            case TICKET -> sendConfirmedTicketEmail(booking);
            case RESERVATION -> sendConfirmedReservationEmail(booking);
            default -> log.warn("Unhandled booking type: {}, not sending email", booking.getBookingType());
        }
    }

    private void sendConfirmedTicketEmail(BookingEntity booking) throws IOException, WriterException {
        Context context = createGenericReservationContext(booking);

        String body = templateService.renderTemplate("inline/emails/confirmed-ticket-email", context);

        String pdfBody = templateService.renderTemplate("inline/attachments/confirmed-ticket-booking-pdf", context);
        EmailAttachment attachment = PdfService.generateA4Pdf(pdfBody,
                getEventAttributeValue(booking.getEvent(), TITLE) + " - Ticket Booking " + booking.getBookingNumber() + ".pdf");

        List<EmailAttachment> entryPasses = createTicketEntryPasses(context, booking);

        entryPasses.add(attachment);

        emailAdapter.sendEmail(EmailContent.builder()
                .to(booking.getOwnerEmail())
                .from(emailProperties.transactional().sender())
                .fromName(emailProperties.transactional().senderName())
                .subject("Your Tickets are Confirmed! See You Soon")
                .body(body)
                .attachments(entryPasses)
                .build());
    }

    private void sendConfirmedReservationEmail(BookingEntity booking) throws IOException, WriterException {
        Context context = createGenericReservationContext(booking);

        String body = templateService.renderTemplate("inline/emails/confirmed-reservation-email", context);

        String pdfBody = templateService.renderTemplate("inline/attachments/confirmed-reservation-booking-pdf", context);

        EmailAttachment attachment = PdfService.generateA4Pdf(pdfBody,
                "Booking Details for " + getEventAttributeValue(booking.getEvent(), TITLE) + ".pdf");

        List<EmailAttachment> entryPasses = createReservationEntryPasses(context, booking);

        entryPasses.add(attachment);

        emailAdapter.sendEmail(EmailContent.builder()
                .to(booking.getOwnerEmail())
                .from(emailProperties.transactional().sender())
                .fromName(emailProperties.transactional().senderName())
                .subject("Your Reservation is Confirmed! See You Soon")
                .body(body)
                .attachments(entryPasses)
                .build());
    }

    private Context createGenericReservationContext(BookingEntity booking) {
        Context context = new Context();

        EventEntity event = booking.getEvent();
        VenueDTO venue = venueInfoService.getVenueDetails(event.getVenueId());

        populateIntro(context, booking, event, venue);

        populateBookingRequestDetails(context, booking, event, venue);

        populateContactInfo(context, venue);

        populatePaymentDetails(context, booking, venue);

        if (booking.getBookingType() == BookingType.TICKET && booking.getStatus() == BookingStatus.CONFIRMED) {
            populateTicketContext(context, booking, venue);

        }

        return context;
    }

    private List<EmailAttachment> createTicketEntryPasses(Context context, BookingEntity booking) throws IOException, WriterException {

        List<EmailAttachment> entryPassAttachments = new ArrayList<>();

        EventEntity event = booking.getEvent();
        VenueDTO venue = venueInfoService.getVenueDetails(event.getVenueId());

        List<EntryPassEntity> entryPasses = entryPassRepository.findEntryPassEntitiesByBooking(booking);
        int index = 0;
        context.setVariable("totalTickets", String.valueOf(entryPasses.size()));
        for (EntryPassEntity entryPass : entryPasses) {

            index++;

            context.setVariable("qrCodeBase64", QrCodeService.generateQRCodeInBase64(String.valueOf(entryPass.getId())));
            context.setVariable("tierName", getBookingTierAttributeValue(entryPass.getBookingTier(), TIER_NAME));
            context.setVariable("currentTicket", String.valueOf(index));

            context.setVariable("ticketPrice", formatCurrency(entryPass.getPrice(), venue.currency().isoCode()));
            context.setVariable("includedDetails", buildIncludedDetails(entryPass.getBookingTier(), venue.currency().isoCode()));
            context.setVariable("ticketReferenceNumber", entryPass.getReferenceNumber());

            String pdfBody = templateService.renderTemplate("inline/attachments/ticket-entry-pass-pdf", context);
            EmailAttachment attachment = PdfService.generateA4Pdf(pdfBody,
                    getEventAttributeValue(booking.getEvent(), TITLE) + " - Ticket " + entryPass.getReferenceNumber() + ".pdf");

            entryPassAttachments.add(attachment);
        }

        return entryPassAttachments;
    }


    private List<EmailAttachment> createReservationEntryPasses(Context context, BookingEntity booking) throws IOException, WriterException {

        List<EmailAttachment> entryPassAttachments = new ArrayList<>();

        List<EntryPassEntity> entryPasses = entryPassRepository.findEntryPassEntitiesByBooking(booking);

        context.setVariable("totalTickets", String.valueOf(entryPasses.size()));

        int index = 0;
        for (EntryPassEntity entryPass : entryPasses) {
            index++;
            context.setVariable("currentTicket", String.valueOf(index));
            context.setVariable("qrCodeBase64", QrCodeService.generateQRCodeInBase64(String.valueOf(entryPass.getId())));
            context.setVariable("ticketReferenceNumber", entryPass.getReferenceNumber());

            String pdfBody = templateService.renderTemplate("inline/attachments/reservation-entry-pass-pdf", context);
            EmailAttachment attachment = PdfService.generateA4Pdf(pdfBody,
                    getEventAttributeValue(booking.getEvent(), TITLE) + " - Pass " + entryPass.getReferenceNumber() + ".pdf");

            entryPassAttachments.add(attachment);
        }

        return entryPassAttachments;
    }

    /*
    Populate context methods
     */
    private void populateIntro(Context context, BookingEntity booking, EventEntity event, VenueDTO venue) {

        context.setVariable("firstName", booking.getOwnerName().split(" ")[0]);
        context.setVariable("eventName", getEventAttributeValue(event, TITLE));
        context.setVariable("venueName", Boolean.TRUE.equals(event.getUseCustomAddress()) ? event.getCustomAddress().getAddressName() : venue.name());
    }

    private void populateContactInfo(Context context, VenueDTO venue) {
        String venueEmail = venue.venueEmail();
        String venuePhone = venue.phoneNumber();

        String contact = venueEmail != null
                ? "at " + venueEmail + " or by phone at " + venuePhone + "."
                : "by phone at " + venuePhone;

        context.setVariable("contactInfo", contact);
    }

    private void populateBookingRequestDetails(Context context, BookingEntity booking, EventEntity event, VenueDTO venue) {

        String isoCode = getIsoCode(venue);
        String timeZone = getTimeZone(venue);

        context.setVariable("fullName", booking.getOwnerName());
        context.setVariable("dateTime", formatInstantInZone(event.getStartDatetime(), timeZone));
        context.setVariable("bookingName", booking.getBookingName());
        context.setVariable("quantity", booking.getBookingItems().stream().mapToInt(BookingItemEntity::getQuantity).sum());
        context.setVariable("bookingNumber", booking.getBookingNumber());

        if (booking.getBookingType() == BookingType.RESERVATION) {
            //Reservations only have one item
            BookingItemEntity reservationItem = booking.getBookingItems().getFirst();
            BookingTierEntity bookingTier = reservationItem.getBookingTier();
            String tierName = getBookingTierAttributeValue(bookingTier, "tier-name");
            BigDecimal minimumSpent = Optional.ofNullable(NumberUtils
                            .createBigDecimal(getNullableBookingTierAttributeValue(bookingTier, MINIMUM_SPENT)))
                    .orElse(BigDecimal.ZERO);
            BigDecimal totalMinimumSpent = minimumSpent.multiply(BigDecimal.valueOf(reservationItem.getQuantity()));

            context.setVariable("tierName", tierName);
            context.setVariable("partySize", reservationItem.getNumOfPeople());
            context.setVariable("minimumSpend", formatCurrency(totalMinimumSpent, isoCode));
            context.setVariable("includedDetails", buildIncludedDetails(bookingTier, isoCode));

            context.setVariable("specialRequests", booking.getComment() != null ? booking.getComment() : "-");
            context.setVariable("occasion", booking.getBookingOccasion() != null ? booking.getBookingOccasion() : "-");
        }

    }

    private void populatePaymentDetails(Context context, BookingEntity booking, VenueDTO venue) {

        String isoCode = getIsoCode(venue);

        context.setVariable("totalAmount", formatCurrency(booking.getTotalAmount(), isoCode));
        context.setVariable("netAmount", formatCurrency(booking.getNetAmount(), isoCode));
        context.setVariable("totalFee", formatCurrency(booking.getTotalFee(), isoCode));

        if (booking.getStatus().equals(BookingStatus.APPROVED)) {
            String paymentLink = paymentAdapter.generatePaymentLinkForEmail(booking);
            context.setVariable("paymentLink", paymentLink);
        }
    }

    private void populateTicketContext(Context context, BookingEntity booking, VenueDTO venue) {

        //create a list of ticket objects
        List<TicketEmailInfo> tickets = new ArrayList<>();
        List<BookingItemEntity> bookingItems = booking.getBookingItems();
        String isoCode = getIsoCode(venue);
        for (BookingItemEntity bookingItem : bookingItems) {

            BigDecimal price = bookingItem.getBookingTier().getPayOnlinePrice();
            BigDecimal totalPrice = price.multiply(new BigDecimal(bookingItem.getQuantity()));

            TicketEmailInfo ticket = TicketEmailInfo.builder()
                    .quantity(bookingItem.getQuantity())
                    .ticketName(getBookingTierAttributeValue(bookingItem.getBookingTier(), TIER_NAME))
                    .totalPrice(formatCurrency(totalPrice, isoCode))
                    .build();
            tickets.add(ticket);
        }

        context.setVariable("tickets", tickets);
    }

    /* HELPER METHODS */
    private String getIsoCode(VenueDTO venue) {
        String isoCode = "EUR";
        if (venue != null && venue.currency() != null && venue.currency().isoCode() != null) {
            isoCode = venue.currency().isoCode();
        }
        return isoCode;
    }

    private String getTimeZone(VenueDTO venue) {
        return Optional.ofNullable(venue).map(VenueDTO::timeZone).orElse("Europe/London");
    }

    /**
     * Builds the "What’s Included" details for the reservation.
     *
     * @param bookingTier The booking entity containing reservation details.
     * @param isoCode     The ISO 4217 currency code (e.g., "EUR", "USD").
     * @return A formatted string describing what’s included.
     */
    private String buildIncludedDetails(BookingTierEntity bookingTier, String isoCode) {

        BigDecimal includedConsumptionAmount = Optional.ofNullable(NumberUtils
                        .createBigDecimal(getNullableBookingTierAttributeValue(bookingTier, INCLUDED_CONSUMPTION_AMOUNT)))
                .orElse(BigDecimal.ZERO);

        String consumptionDesc = getNullableBookingTierAttributeValue(bookingTier, "included-consumption-description");
        String tierDescription = getNullableBookingTierAttributeValue(bookingTier, "description");

        String includedDetails = Stream.of(
                        bookingTier.getType().equals(BookingTierType.RESERVATION)
                                ? String.format("Includes access for %d-%d people.", bookingTier.getMinPersons(), bookingTier.getMaxPersons())
                                : null,
                        !includedConsumptionAmount.equals(BigDecimal.ZERO)
                                ? String.format("Includes %s as credit to spend.", formatCurrency(includedConsumptionAmount, isoCode))
                                : null,
                        ensureEndingPunctuation(consumptionDesc),
                        ensureEndingPunctuation(tierDescription)
                )
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(" "));

        return StringUtils.isEmpty(includedDetails) ? "-" : includedDetails;
    }

    private String ensureEndingPunctuation(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }

        String trimmed = text.trim();
        return trimmed.endsWith(".") || trimmed.endsWith("!") || trimmed.endsWith("?")
                ? trimmed
                : trimmed + ".";
    }

    private String getEventAttributeValue(EventEntity event, String attributeName) {
        EventAttributeEntity attribute = eventAttributeService
                .getEventAttributeByName(event.getEventAttributes(), attributeName);
        return attribute != null ? attribute.getAttributeValue() : "-";
    }

    private String getBookingTierAttributeValue(BookingTierEntity tier, String attributeName) {
        BookingTierAttributeEntity attribute = bookingTierAttributeService
                .getBookingTierAttributeByName(tier.getBookingTierAttributes(), attributeName);
        return attribute != null ? attribute.getAttributeValue() : "-";
    }

    private String getNullableBookingTierAttributeValue(BookingTierEntity tier, String attributeName) {
        BookingTierAttributeEntity attribute = bookingTierAttributeService
                .getBookingTierAttributeByName(tier.getBookingTierAttributes(), attributeName);
        return attribute != null ? attribute.getAttributeValue() : null;
    }

    private boolean isInvalidBooking(BookingEntity booking) {
        return booking == null ||
                booking.getOwnerEmail() == null ||
                booking.getBookingChannel() == BookingChannel.ADMIN_DASHBOARD;
    }
}