package com.xo.backend.client.email.javamail;

import com.xo.backend.client.email.EmailAdapter;
import com.xo.backend.client.email.dto.EmailAttachment;
import com.xo.backend.client.email.dto.EmailContent;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ConditionalOnProperty(name = "app.clients.email.provider", havingValue = "java-mail")
@RequiredArgsConstructor
public class JavaMailAdapter implements EmailAdapter {

    private final JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String sender;

    @Async("emailExecutor")
    @Override
    public void sendEmail(EmailContent emailContent) {
        try {
            log.info("Starting async email process v2 to {}", emailContent.to());
            MimeMessage message = createMimeMessage(emailContent);
            mailSender.send(message);
            log.info("Email successfully sent to {}", emailContent.to());
        } catch (Exception e) {
            log.error("Failed to send email to {}", emailContent.to(), e);
        }
    }

    private MimeMessage createMimeMessage(EmailContent emailContent) throws MessagingException {
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

        // Set email subject, sender, and recipient
        helper.setSubject(emailContent.subject());
        helper.setFrom(sender);
        helper.setTo(emailContent.to());

        // Set HTML content
        helper.setText(emailContent.body(), true);

        // Add attachments if any
        if (emailContent.attachments() != null) {
            for (EmailAttachment attachment : emailContent.attachments()) {
                helper.addAttachment(
                        attachment.fileName(),
                        new ByteArrayResource(attachment.content()),
                        attachment.contentType()
                );
            }
        }

        return mimeMessage;
    }
}