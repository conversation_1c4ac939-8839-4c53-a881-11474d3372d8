package com.xo.backend.client.email;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.xo.backend.client.email.dto.EmailAttachment;
import com.xo.backend.error.exceptions.GeneralException;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public interface PdfService {

    static EmailAttachment generateA4Pdf(String html, String fileName) {
        try {
            byte[] pdfBytes = convertHtmlToA4Pdf(html);

            return new EmailAttachment(pdfBytes, fileName, "application/pdf");
        } catch (IOException e) {
            throw new GeneralException("Failed to generate PDF: " + fileName + "due to: " + e);
        }
    }

    private static byte[] convertHtmlToA4Pdf(String htmlContent) throws IOException {
        try (ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream()) {
            PdfWriter writer = new PdfWriter(pdfOutputStream);
            PdfDocument pdfDocument = new PdfDocument(writer);

            // Set the page size to A4
            pdfDocument.setDefaultPageSize(PageSize.A4);

            // Create converter properties to handle page margins
            ConverterProperties converterProperties = new ConverterProperties();

            // Convert HTML to PDF with the specified properties
            HtmlConverter.convertToPdf(htmlContent, pdfDocument, converterProperties);

            return pdfOutputStream.toByteArray();
        }
    }
}
