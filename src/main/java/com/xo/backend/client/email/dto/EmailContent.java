package com.xo.backend.client.email.dto;

import lombok.Builder;

import java.util.List;

@Builder
public record EmailContent(
        String to, // Recipient's email address
        String from,
        String fromName,
        String replyTo,
        String subject, // Email subject
        String body, // Email body (HTML content)
        List<EmailAttachment> attachments// List of attachments (e.g., PDFs)
) {
}