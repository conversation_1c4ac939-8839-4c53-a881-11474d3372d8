package com.xo.backend.client.venues.go;

import com.xo.backend.client.go.GoPropertiesService;
import com.xo.backend.client.go.GoRequestBuilder;
import com.xo.backend.client.venues.VenueInfoAdapter;
import com.xo.backend.error.exceptions.GoErrorResponseException;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(prefix = "app.clients.venue-info", name = "provider", havingValue = "go")
public class GoVenueInfoAdapter implements VenueInfoAdapter {

    @Qualifier("goRestClient")
    private final RestTemplate goRestTemplate;
    private final GoRequestBuilder requestBuilder;
    private final GoPropertiesService propertiesService;


    public VenueDTO requestVenueDetails(String venueId) {
        String url = propertiesService.getVenueDetailsUrl(venueId);

        HttpEntity<MultiValueMap<String, String>> entity = requestBuilder.buildHttpEntityForWithApiKey();

        ResponseEntity<VenueDTO> venueResponse = goRestTemplate.exchange(url, HttpMethod.GET, entity, VenueDTO.class);

        VenueDTO venueDetails = venueResponse.getBody();

        if (venueDetails == null) {
            throw new GoErrorResponseException("Venue not found: " + venueId, (HttpStatus) venueResponse.getStatusCode());
        }
        return venueDetails;
    }

    @Override
    public Map<Long, VenueOverviewDTO> requestVenuesOverview(Collection<Long> venueIds) {
        if(venueIds==null || venueIds.isEmpty()){
            return Collections.emptyMap();
        }

        String url = propertiesService.getVenueOverview();
        HttpEntity<MultiValueMap<String, String>> entity = requestBuilder.buildHttpEntityForWithApiKey();
        HttpEntity<List<Long>> updatedEntity = new HttpEntity<>(new ArrayList<>(venueIds), entity.getHeaders());

        try {
            ResponseEntity<List<VenueOverviewDTO>> venueResponse = goRestTemplate.exchange(
                    url, HttpMethod.POST, updatedEntity, new ParameterizedTypeReference<>() {});

            return Optional.ofNullable(venueResponse.getBody())
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(
                            VenueOverviewDTO::storeId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        }catch (Exception e) {
            log.error("Error while requesting venues overview: {}", e.getMessage(), e);
            return  Collections.emptyMap();
        }
    }
}
