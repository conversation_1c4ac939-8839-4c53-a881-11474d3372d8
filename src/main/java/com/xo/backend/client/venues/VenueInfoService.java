package com.xo.backend.client.venues;

import com.xo.backend.model.dto.go.VenueOverviewDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.xo.backend.model.dto.go.VenueDTO;

import java.util.Collection;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class VenueInfoService {

    private final VenueInfoAdapter venueInfoAdapter;

    public VenueDTO getVenueDetails(String venueId) {
        return venueInfoAdapter.requestVenueDetails(venueId);
    }

    public Map<Long, VenueOverviewDTO> getVenuesOverview(Collection<Long> venueIds) {
        return venueInfoAdapter.requestVenuesOverview(venueIds);
    }
}
