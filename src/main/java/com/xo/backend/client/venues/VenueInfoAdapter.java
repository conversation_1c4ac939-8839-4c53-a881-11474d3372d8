package com.xo.backend.client.venues;

import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;

import java.util.Collection;
import java.util.Map;

public interface VenueInfoAdapter {
    /**
     * Request detailed information about a specific venue
     *
     * @param venueId The ID of the venue
     * @return Venue details
     */
    VenueDTO requestVenueDetails(String venueId);

    /**
     * Request overview information for multiple venues
     *
     * @param venueIds Collection of venue IDs
     * @return Map of venue ID to venue overview
     */
    Map<Long, VenueOverviewDTO> requestVenuesOverview(Collection<Long> venueIds);
}
