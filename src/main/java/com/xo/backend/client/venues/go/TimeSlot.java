package com.xo.backend.client.venues.go;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * Immutable class representing a time slot within a day
 */
@Getter
@RequiredArgsConstructor
public class TimeSlot {
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final String NEXT_DAY_SUFFIX = " (next day)";

    // Maximum gap between slots to still consider them adjacent (in minutes)
    private static final int MAX_GAP_MINUTES = 1;

    private final LocalTime start;
    private final LocalTime end;
    private final boolean overnight;

    public TimeSlot(LocalTime start, LocalTime end) {
        this(start, end, false);
    }

    /**
     * Returns true if this slot overlaps or is adjacent to another.
     * <p>Precondition: other.getStart() ≥ this.getStart().
     */
    public boolean overlapsOrAdjacent(TimeSlot other) {

        if (other.getStart().isBefore(this.getStart())) {
            throw new IllegalArgumentException("Slots must be sorted by start time before calling overlapsOrAdjacent");
        }

        if (this.overnight || other.overnight) {
            // For simplicity, overnight slots are only merged with non-overnight slots
            // that don't cause logical conflicts
            return false;
        }

        // Calculate the gap between this slot's end and the other slot's start
        // Negative value means overlap, 0 means exactly adjacent, positive means gap
        Duration gap = Duration.between(this.end, other.start);

        // Merge if there's overlap (negative), exact adjacency (0), or small gap (<=MAX_GAP_MINUTES)
        return gap.toMinutes() <= MAX_GAP_MINUTES;
    }

    /**
     * Merge this slot with another one, creating a new TimeSlot
     * Assumes the slots are overlapping or adjacent
     */
    public TimeSlot merge(TimeSlot other) {
        boolean resultIsOvernight = this.overnight || other.overnight;
        LocalTime mergedStart = this.start.isBefore(other.start) ? this.start : other.start;
        LocalTime mergedEnd = this.end.isAfter(other.end) ? this.end : other.end;

        return new TimeSlot(mergedStart, mergedEnd, resultIsOvernight);
    }

    /**
     * Format the time slot as a string
     */
    public String format() {
        String timeStr = start.format(TIME_FORMATTER) + "-" + end.format(TIME_FORMATTER);
        return overnight ? timeStr + NEXT_DAY_SUFFIX : timeStr;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TimeSlot timeSlot)) return false;

        return overnight == timeSlot.overnight
                && start.equals(timeSlot.start)
                && end.equals(timeSlot.end);
    }

    @Override
    public int hashCode() {
        int result = start.hashCode();
        result = 31 * result + end.hashCode();
        result = 31 * result + (overnight ? 1 : 0);
        return result;
    }

    @Override
    public String toString() {
        return format();
    }
}