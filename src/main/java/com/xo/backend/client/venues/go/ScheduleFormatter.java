package com.xo.backend.client.venues.go;

import com.xo.backend.model.dto.responses.venueresponse.Schedule;
import com.xo.backend.model.dto.responses.venueresponse.ScheduleAvailability;
import com.xo.backend.model.dto.responses.venueresponse.SpecialSchedule;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service to format venue schedules into readable strings
 */
public final class ScheduleFormatter {

    private static final LocalTime MIDNIGHT = LocalTime.of(0, 0);
    private static final LocalTime ALMOST_MIDNIGHT = LocalTime.of(23, 59);
    private static final LocalTime EARLY_MORNING_CUTOFF = LocalTime.of(6, 0);
    private static final String OPENING_HOURS_TYPE = "OPENING_HOURS";
    private static final String CLOSED = "Closed";

    private static final Map<DayOfWeek, String> DAY_TO_CODE = Map.of(
            DayOfWeek.MONDAY, "MON",
            DayOfWeek.TUESDAY, "TUE",
            DayOfWeek.WEDNESDAY, "WED",
            DayOfWeek.THURSDAY, "THU",
            DayOfWeek.FRIDAY, "FRI",
            DayOfWeek.SATURDAY, "SAT",
            DayOfWeek.SUNDAY, "SUN"
    );

    private static final List<DayOfWeek> ORDERED_DAYS = List.of(
            DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY,
            DayOfWeek.THURSDAY, DayOfWeek.FRIDAY, DayOfWeek.SATURDAY, DayOfWeek.SUNDAY
    );

    private ScheduleFormatter() {
        throw new AssertionError("Utility class should not be instantiated");
    }

    /**
     * Transforms venue schedules into a map of day names to comma-separated schedule strings
     * Handles overnight schedules by detecting times that cross midnight
     */
    public static Map<String, String> formatVenueSchedules(List<SpecialSchedule> specialSchedules) {
        if (specialSchedules == null || specialSchedules.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, List<TimeSlot>> dayCodeToSlots = groupAvailabilitiesByDayCode(specialSchedules);

        mergeOverlappingSlotsPerDay(dayCodeToSlots);

        processOvernightSchedules(dayCodeToSlots);

        return formatResultWithAllDays(dayCodeToSlots);
    }

    private static Map<String, List<TimeSlot>> groupAvailabilitiesByDayCode(List<SpecialSchedule> specialSchedules) {
        Map<String, List<TimeSlot>> dayCodeToSlots = new HashMap<>();

        specialSchedules.stream()
                .filter(schedule -> OPENING_HOURS_TYPE.equals(schedule.type()))
                .map(SpecialSchedule::schedule)
                .filter(Objects::nonNull)
                .map(Schedule::availabilities)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(ScheduleFormatter::isValidAvailability)
                .forEach(availability -> addAvailabilityToMap(availability, dayCodeToSlots));

        return dayCodeToSlots;
    }

    private static boolean isValidAvailability(ScheduleAvailability availability) {
        return availability.daysOfWeek() != null
                && availability.startTime() != null
                && availability.endTime() != null;
    }

    private static void addAvailabilityToMap(ScheduleAvailability availability, Map<String, List<TimeSlot>> dayCodeToSlots) {
        LocalTime start = LocalTime.parse(availability.startTime());
        LocalTime end = LocalTime.parse(availability.endTime());

        for (String dayCode : availability.daysOfWeek()) {
            dayCodeToSlots.computeIfAbsent(dayCode, k -> new ArrayList<>())
                    .add(new TimeSlot(start, end));
        }
    }

    private static void mergeOverlappingSlotsPerDay(Map<String, List<TimeSlot>> dayToSlots) {
        for (Map.Entry<String, List<TimeSlot>> entry : dayToSlots.entrySet()) {
            List<TimeSlot> slots = entry.getValue();
            if (slots.size() > 1) {
                slots.sort(Comparator.comparing(TimeSlot::getStart));
                List<TimeSlot> mergedSlots = mergeTimeSlots(slots);
                entry.setValue(mergedSlots);
            }
        }
    }

    private static List<TimeSlot> mergeTimeSlots(List<TimeSlot> sortedSlots) {
        if (sortedSlots.isEmpty()) {
            return Collections.emptyList();
        }

        List<TimeSlot> mergedSlots = new ArrayList<>();
        TimeSlot current = sortedSlots.getFirst();

        for (int i = 1; i < sortedSlots.size(); i++) {
            TimeSlot next = sortedSlots.get(i);

            if (current.overlapsOrAdjacent(next)) {
                current = current.merge(next);
            } else {
                mergedSlots.add(current);
                current = next;
            }
        }

        mergedSlots.add(current);
        return mergedSlots;
    }

    private static void processOvernightSchedules(Map<String, List<TimeSlot>> dayCodeToSlots) {
        List<String> orderedDayCodes = List.of("MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN");

        for (int i = 0; i < orderedDayCodes.size(); i++) {
            String currentDayCode = orderedDayCodes.get(i);
            String nextDayCode = orderedDayCodes.get((i + 1) % orderedDayCodes.size());

            if (!dayCodeToSlots.containsKey(currentDayCode) || !dayCodeToSlots.containsKey(nextDayCode)) {
                continue;
            }

            processOvernightForDayPair(dayCodeToSlots, currentDayCode, nextDayCode);
        }
    }

    private static void processOvernightForDayPair(Map<String, List<TimeSlot>> dayToSlots,
                                                   String currentDayName,
                                                   String nextDayName) {
        List<TimeSlot> currentDaySlots = dayToSlots.get(currentDayName);
        List<TimeSlot> nextDaySlots = dayToSlots.get(nextDayName);

        if (currentDaySlots == null || nextDaySlots == null) {
            return;
        }

        Optional<TimeSlot> lateNightSlot = findLateNightSlot(currentDaySlots);
        Optional<TimeSlot> earlyMorningSlot = findEarlyMorningSlot(nextDaySlots);

        if (lateNightSlot.isPresent() && earlyMorningSlot.isPresent() &&
                earlyMorningSlot.get().getEnd().isBefore(EARLY_MORNING_CUTOFF)) {
            createOvernightSlot(currentDaySlots, nextDaySlots, lateNightSlot.get(), earlyMorningSlot.get());
        }
    }

    private static Optional<TimeSlot> findLateNightSlot(List<TimeSlot> slots) {
        return slots.stream()
                .filter(slot -> slot.getEnd().equals(ALMOST_MIDNIGHT) || slot.getEnd().equals(LocalTime.MAX))
                .max(Comparator.comparing(TimeSlot::getStart));
    }

    private static Optional<TimeSlot> findEarlyMorningSlot(List<TimeSlot> slots) {
        return slots.stream()
                .filter(slot -> slot.getStart().equals(MIDNIGHT))
                .min(Comparator.comparing(TimeSlot::getStart));
    }

    private static void createOvernightSlot(List<TimeSlot> currentDaySlots,
                                            List<TimeSlot> nextDaySlots,
                                            TimeSlot lateSlot,
                                            TimeSlot earlySlot) {
        TimeSlot overnightSlot = new TimeSlot(lateSlot.getStart(), earlySlot.getEnd(), true);

        currentDaySlots.remove(lateSlot);

        nextDaySlots.remove(earlySlot);

        currentDaySlots.add(overnightSlot);
    }

    private static Map<String, String> formatResultWithAllDays(Map<String, List<TimeSlot>> dayCodeToSlots) {
        Map<String, String> result = new LinkedHashMap<>();

        for (DayOfWeek dayOfWeek : ORDERED_DAYS) {
            String dayCode = DAY_TO_CODE.get(dayOfWeek);
            String localizedDayName = dayOfWeek.getDisplayName(TextStyle.FULL, Locale.ENGLISH);
            List<TimeSlot> slots = dayCodeToSlots.getOrDefault(dayCode, Collections.emptyList());
            result.put(localizedDayName, formatDaySlots(slots));
        }

        return result;
    }

    private static String formatDaySlots(List<TimeSlot> slots) {
        if (slots.isEmpty()) {
            return CLOSED;
        }

        return slots.stream()
                .map(TimeSlot::format)
                .collect(Collectors.joining(", "));
    }
}