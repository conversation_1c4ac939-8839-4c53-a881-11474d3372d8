package com.xo.backend.client.user;

import com.xo.backend.model.dto.UserPermissionsResponseDTO;
import com.xo.backend.model.dto.go.GoUserProfileResponseDTO;

public interface UserInfoAdapter {
    /**
     * Request user permissions using a token
     *
     * @param token Authentication token
     * @return User permissions response
     */
    UserPermissionsResponseDTO requestUserPermissions(String token);

    /**
     * Request the profile of the currently authenticated user
     *
     * @return User profile response
     */
    GoUserProfileResponseDTO requestUserProfile();
}
