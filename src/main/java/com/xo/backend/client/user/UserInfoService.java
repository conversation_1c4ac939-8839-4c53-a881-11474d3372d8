package com.xo.backend.client.user;

import com.xo.backend.model.dto.UserPermissionsResponseDTO;
import com.xo.backend.model.dto.go.GoUserProfileResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserInfoService {

    private final UserInfoAdapter userInfoAdapter;

    public UserPermissionsResponseDTO getUserPermissions(String token) {
        return userInfoAdapter.requestUserPermissions(token);
    }

    public GoUserProfileResponseDTO getUserProfile() {
        return userInfoAdapter.requestUserProfile();
    }

}