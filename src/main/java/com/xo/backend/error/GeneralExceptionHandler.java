package com.xo.backend.error;

import com.google.common.base.Throwables;
import com.xo.backend.error.exceptions.*;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
public class GeneralExceptionHandler {

    private static final String PROCESSING_LOG_TEMPLATE = "Processing error -> {}, with stacktrace -> {}";
    private static final String DEFAULT_ERROR_MESSAGE = "Something went wrong";
    private static final Integer MAX_LINES = 10;
    private static final Integer MAX_LENGTH = 1000;

    private ResponseEntity<ErrorResponseInfo> createErrorResponseInfo(String message, HttpStatusCode status, String errorCode, Throwable e) {
        ErrorResponseInfo errorResponseInfo = ErrorResponseInfo.builder()
                .message(message)
                .status(status.value())
                .errorCode(errorCode)
                .timestamp(Instant.now().toEpochMilli())
                .build();
        String stackTrace = truncateStackTrace(Throwables.getStackTraceAsString(e));
        log.error(PROCESSING_LOG_TEMPLATE, errorResponseInfo.message(), stackTrace);
        return ResponseEntity.status(status).body(errorResponseInfo);
    }

    private static String truncateStackTrace(String stackTrace) {
        String[] lines = stackTrace.split("\n");
        String truncated = Arrays.stream(lines)
                .limit(MAX_LINES)
                .collect(Collectors.joining("\n"));

        if (truncated.length() > MAX_LENGTH) {
            truncated = truncated.substring(0, MAX_LENGTH) + "...";
        } else if (lines.length > MAX_LENGTH) {
            truncated += "\n...";
        }

        return truncated;
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponseInfo> handleException(Exception e) {
        HttpStatusCode status = HttpStatus.INTERNAL_SERVER_ERROR;
        String errorCode = ResponseErrorCode.INTERNAL_SERVER_ERROR.name();
        String message = e.getMessage() != null ? e.getMessage() : DEFAULT_ERROR_MESSAGE;

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    //maybe we don't want to even log this exception, or return body
    @ExceptionHandler(AuthorizationDeniedException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(AuthorizationDeniedException e) {

        HttpStatusCode status = HttpStatus.UNAUTHORIZED;
        String errorCode = ResponseErrorCode.UNAUTHORIZED.name();
        String message = "Not authorized";

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(HttpClientErrorException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(HttpClientErrorException e) {

        HttpStatusCode status = e.getStatusCode();
        String errorCode = ResponseErrorCode.EXTERNAL_API_ERROR.name();
        String message = "External API error";

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(GeneralException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(GeneralException e) {

        HttpStatusCode status = e.getStatus();
        String errorCode = e.getStatus().name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(EventNotFoundException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(EventNotFoundException e) {

        HttpStatusCode status = HttpStatus.NOT_FOUND;
        String errorCode = ResponseErrorCode.NOT_FOUND.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(CsvExportException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(CsvExportException e) {

        HttpStatusCode status = HttpStatus.INTERNAL_SERVER_ERROR;
        String errorCode = ResponseErrorCode.INTERNAL_SERVER_ERROR.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(PreSaleDiscountEndedException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(PreSaleDiscountEndedException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.PRE_SALE_EXPIRED.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }


    @ExceptionHandler(InvalidCountryCodeException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(InvalidCountryCodeException e) {

        String message = e.getMessage();

        return createErrorResponseInfo(message, e.getHttpStatus(),e.getErrorCode().name(),e);
    }

    @ExceptionHandler(StripeSessionNotFoundException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(StripeSessionNotFoundException e) {

        HttpStatusCode status = HttpStatus.INTERNAL_SERVER_ERROR;
        String errorCode = ResponseErrorCode.INTERNAL_SERVER_ERROR.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(StripeSessionExpirationException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(StripeSessionExpirationException e) {

        HttpStatusCode status = HttpStatus.INTERNAL_SERVER_ERROR;
        String errorCode = ResponseErrorCode.INTERNAL_SERVER_ERROR.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(BookingPaymentSessionExistsException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(BookingPaymentSessionExistsException e) {

        HttpStatusCode status = HttpStatus.INTERNAL_SERVER_ERROR;
        String errorCode = ResponseErrorCode.INTERNAL_SERVER_ERROR.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(InvalidBookingStatusException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(InvalidBookingStatusException e) {

        HttpStatusCode status = HttpStatus.CONFLICT;
        String errorCode = ResponseErrorCode.BAD_BOOKING_STATUS.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(InsufficientPermissionsException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(InsufficientPermissionsException e) {

        HttpStatusCode status = HttpStatus.FORBIDDEN;
        String errorCode = ResponseErrorCode.UNAUTHORIZED.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(DuplicateBookingException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(DuplicateBookingException e) {

        HttpStatusCode status = HttpStatus.CONFLICT;
        String errorCode = ResponseErrorCode.DUPLICATE_BOOKING.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(BookingNotFoundException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(BookingNotFoundException e) {

        HttpStatusCode status = HttpStatus.NOT_FOUND;
        String errorCode = ResponseErrorCode.NOT_FOUND.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(BookingRequestException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(BookingRequestException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.BAD_BOOKING_REQUEST.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(MethodArgumentNotValidException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.REQUEST_PARAMS_VALIDATION_ERROR.name();
        String message = getMessage(e);

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(ConstraintViolationException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.REQUEST_PARAMS_VALIDATION_ERROR.name();
        String message = getConstraintViolationMessage(e);

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(BookingTierNotFoundException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(BookingTierNotFoundException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.REQUEST_PARAMS_VALIDATION_ERROR.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(BookingTierRequestException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(BookingTierRequestException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.REQUEST_PARAMS_VALIDATION_ERROR.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(EventRequestException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(EventRequestException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.REQUEST_PARAMS_VALIDATION_ERROR.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(FileUploadException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(FileUploadException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.REQUEST_PARAMS_VALIDATION_ERROR.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    private String getMessage(MethodArgumentNotValidException e) {
        Map<String, String> errors = new HashMap<>();
        StringBuilder message = new StringBuilder();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();

            // Handle NonNull errors separately
            if ("NotNull".equals(error.getCode())) {
                message.append(fieldName).append(" ").append(errorMessage).append(", ");
            } else {
                // Handle other errors
                errors.put(fieldName, errorMessage);
            }
        });

        if(!message.isEmpty() && errors.isEmpty()){
            message.setLength(message.length() - 2); // Remove the trailing comma and space from the NonNull messages
        }
        else{
            message.append(errors);
        }

        return message.toString();
    }

    private String getConstraintViolationMessage(ConstraintViolationException e) {
        StringBuilder message = new StringBuilder();

        for (ConstraintViolation<?> violation : e.getConstraintViolations()) {
            String propertyPath = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();

            // Remove method name prefix from property path (e.g., "createOrUpdateBatch.arg0[0].feeConfig.appFeePerc" -> "feeConfig.appFeePerc")
            String cleanPropertyPath = cleanPropertyPath(propertyPath);

            message.append(cleanPropertyPath).append(": ").append(errorMessage).append("; ");
        }

        if (!message.isEmpty()) {
            // Remove the trailing semicolon and space
            message.setLength(message.length() - 2);
        }

        return message.toString();
    }

    private String cleanPropertyPath(String propertyPath) {
        // Remove method name and argument prefixes like "createOrUpdateBatch.arg0[0]."
        // Keep only the meaningful part like "feeConfig.appFeePerc"
        if (propertyPath.contains(".")) {
            String[] parts = propertyPath.split("\\.");
            StringBuilder cleanPath = new StringBuilder();

            for (int i = 0; i < parts.length; i++) {
                String part = parts[i];
                // Skip method names and argument patterns
                if (part.matches("arg\\d+.*") || part.matches("\\w+\\[\\d+]")) {
                    continue;
                }
                // Skip method names (first part before arguments)
                if (i == 0 && parts.length > 1 && (parts[1].matches("arg\\d+.*") || parts[1].matches("\\w+\\[\\d+]"))) {
                    continue;
                }

                if (!cleanPath.isEmpty()) {
                    cleanPath.append(".");
                }
                cleanPath.append(part);
            }

            return cleanPath.toString();
        }

        return propertyPath;
    }

    @ExceptionHandler(PaymentRequiredException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(PaymentRequiredException e) {

        HttpStatusCode status = HttpStatus.PAYMENT_REQUIRED;
        String errorCode = ResponseErrorCode.PAYMENT_REQUIRED.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(InvalidBookingConfirmationRequirementException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(InvalidBookingConfirmationRequirementException e) {

        HttpStatusCode status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.INVALID_CONFIRMATION_REQUIREMENT.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(InvalidBookingTypeException.class)
    public ResponseEntity<ErrorResponseInfo> handleInvalidBookingTypeException(InvalidBookingTypeException e) {

        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.INVALID_BOOKING_TYPE.name();
        String message = e.getMessage();

        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(EntryPassNotFoundException.class)
    public ResponseEntity<ErrorResponseInfo> handleEntryPassNotFoundException(EntryPassNotFoundException e) {
        HttpStatus status = HttpStatus.NOT_FOUND;
        String errorCode = String.valueOf(ResponseErrorCode.ENTRY_PASS_NOT_FOUND);
        String message = e.getMessage();
        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(BookingItemNotFoundException.class)
    public ResponseEntity<ErrorResponseInfo> handleBookingItemNotFoundException(BookingItemNotFoundException e) {
        HttpStatus status = HttpStatus.NOT_FOUND;
        String errorCode = String.valueOf(ResponseErrorCode.BOOKING_ITEM_NOT_FOUND);
        String message = e.getMessage();
        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(InvalidStatusTransitionException.class)
    public ResponseEntity<String> handleInvalidStatusTransition(InvalidStatusTransitionException ex) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ex.getMessage());
    }

    @ExceptionHandler(UnauthorizedAccessException.class)
    public ResponseEntity<String> handleUnauthorizedAccessException(UnauthorizedAccessException ex) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ex.getMessage());
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorResponseInfo> handleIllegalArgumentException(MethodArgumentTypeMismatchException e) {
        String parameterName = e.getName();
        String message = String.format("Invalid value for parameter '%s'", parameterName);
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = String.valueOf(ResponseErrorCode.BAD_REQUEST);
        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(HttpMessageNotReadableException e) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = String.valueOf(ResponseErrorCode.BAD_REQUEST);
        String message = e.getMessage();
        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(BookingStatusException.class)
    public ResponseEntity<ErrorResponseInfo> handleBookingStatusException(BookingStatusException e) {
        String message = e.getMessage();
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = String.valueOf(ResponseErrorCode.BAD_REQUEST);
        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(EventAssociationException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(EventAssociationException e) {
        String message = e.getMessage();
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = String.valueOf(ResponseErrorCode.BAD_REQUEST);
        return createErrorResponseInfo(message, status, errorCode, e);
    }


    @ExceptionHandler(InvalidCurrencyException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(InvalidCurrencyException e) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = String.valueOf(ResponseErrorCode.INVALID_CURRENCY);
        String message = e.getMessage();
        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(PaymentRequestException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(PaymentRequestException e) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = String.valueOf(ResponseErrorCode.PAYMENT_ERROR);
        String message = e.getMessage();
        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(BusinessException e) {
        String errorCode = e.getErrorCode().name();
        String message = e.getMessage();
        return createErrorResponseInfo(message, e.getHttpStatus(), errorCode, e);
    }

    @ExceptionHandler(CustomAddressRequestException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(CustomAddressRequestException e) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = String.valueOf(ResponseErrorCode.PAYMENT_ERROR);
        String message = e.getMessage();
        return createErrorResponseInfo(message, status, errorCode, e);
    }
    @ExceptionHandler(PriceModificationException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(PriceModificationException e) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String errorCode = ResponseErrorCode.PRICE_MODIFICATION_NOT_ALLOWED.name();
        String message = e.getMessage();
        return createErrorResponseInfo(message,status,errorCode,e);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(ResourceNotFoundException e) {
        HttpStatus status = HttpStatus.NOT_FOUND;
        String errorCode = String.valueOf(ResponseErrorCode.NOT_FOUND);
        String message = e.getMessage();
        return createErrorResponseInfo(message, status, errorCode, e);
    }

    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ErrorResponseInfo> handleException(IllegalStateException e) {
        HttpStatus status = HttpStatus.CONFLICT;
        String errorCode = String.valueOf(ResponseErrorCode.ILLEGAL_STATE);
        String message = e.getMessage();
        return createErrorResponseInfo(message, status, errorCode, e);
    }

}
