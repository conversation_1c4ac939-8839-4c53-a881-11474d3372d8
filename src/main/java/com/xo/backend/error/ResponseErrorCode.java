package com.xo.backend.error;

public enum ResponseErrorCode {
    INTERNAL_SERVER_ERROR,
    UNAUT<PERSON><PERSON>IZ<PERSON>,
    EXTERNAL_API_ERROR,
    NOT_FOUND,
    BAD_BOOKING_STATUS,
    DUPLICATE_BOOKING,
    BAD_BOOKING_REQUEST,
    PRE_SALE_EXPIRED,
    REQUEST_PARAMS_VALIDATION_ERROR,
    PAYMENT_REQUIRED,
    INVALID_CONFIRMATION_REQUIREMENT,
    INVALID_BOOKING_TYPE,
    ENTRY_PASS_NOT_FOUND,
    BOOKING_ITEM_NOT_FOUND,
    BAD_REQUEST,
    INVALID_CURRENCY,
    PAYMENT_ERROR,
    PRICE_MODIFICATION_NOT_ALLOWED,
    ILLEGAL_STATE
}
