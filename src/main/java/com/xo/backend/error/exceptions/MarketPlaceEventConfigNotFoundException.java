package com.xo.backend.error.exceptions;

import com.xo.backend.error.ResponseErrorCode;
import org.springframework.http.HttpStatus;

public class MarketPlaceEventConfigNotFoundException extends BusinessException {
    private static final String MESSAGE = "MarketPlaceEventConfig not found";

    public MarketPlaceEventConfigNotFoundException() {
        super(MESSAGE, HttpStatus.NOT_FOUND, ResponseErrorCode.NOT_FOUND);
    }
}
