package com.xo.backend.error.exceptions;

import com.xo.backend.error.ResponseErrorCode;
import lombok.Getter;
import org.springframework.http.HttpStatus;

public abstract class BusinessException extends RuntimeException {
    @Getter private final HttpStatus httpStatus;
    @Getter private final ResponseErrorCode errorCode;

    public BusinessException(String errorMessage, HttpStatus httpStatus, ResponseErrorCode errorCode) {
        super(errorMessage);
        this.httpStatus = httpStatus;
        this.errorCode = errorCode;
    }
}
