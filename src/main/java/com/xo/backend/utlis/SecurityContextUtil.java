package com.xo.backend.utlis;

import com.xo.backend.error.exceptions.InsufficientPermissionsException;
import com.xo.backend.security.TokenAuthentication;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.Collection;

@UtilityClass
public class SecurityContextUtil {

    private static final String STORE_PREFIX = "STORE_";

    //admin permissions on store level
    private static final String STORE_ADMIN_SUFFIX = "_STORE_ADMIN";
    private static final String STORE_STANDARD_SUFFIX = "_STORE_STANDARD";
    private static final String TENANT_ADMIN_SUFFIX = "_TENANT_ADMIN";

    //admin roles
    private static final String ROLE_ADMIN = "ROLE_ADMIN";
    private static final String ROLE_PLATFORM_ADMIN = "ROLE_PLATFORM_ADMIN";
    private static final String ROLE_TENANT_ADMIN = "ROLE_TENANT_ADMIN";

    public Integer getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return (authentication != null) ? (Integer) authentication.getPrincipal() : null;
    }

    public String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return (authentication instanceof TokenAuthentication) ? ((TokenAuthentication) authentication).getUsername() : null;
    }

    public Collection<? extends GrantedAuthority> getCurrentUserAuthorities() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null && authentication.isAuthenticated()) {
            return authentication.getAuthorities();
        }
        return new ArrayList<>(); // or return an empty list
    }

    public void validateVenueAccess(String venueId) {
        Collection<? extends GrantedAuthority> authorities = SecurityContextUtil.getCurrentUserAuthorities();

        // Super admin, platform admin and tenant admin have access to all venues
        if (hasAdminPrivileges(authorities)) {
            return;
        }

        boolean hasVenueAccess = authorities.stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(auth -> isStoreAdminForVenue(auth, venueId));

        if (!hasVenueAccess) {
            throw new InsufficientPermissionsException();
        }
    }

    public boolean hasAdminPrivileges() {
        return getCurrentUserAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(auth -> auth.equals(ROLE_ADMIN)
                        || auth.equals(ROLE_PLATFORM_ADMIN)
                        || auth.equals(ROLE_TENANT_ADMIN));
    }

    public boolean hasStoreAdminPrivileges(String storeId) {
        return getCurrentUserAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(auth -> auth.equals(STORE_PREFIX + storeId + STORE_ADMIN_SUFFIX)
                        || auth.equals(STORE_PREFIX + storeId + STORE_STANDARD_SUFFIX)
                        || auth.equals(STORE_PREFIX + storeId + TENANT_ADMIN_SUFFIX));
    }

    public boolean hasAdminOrStoreAdminPrivileges(String storeId) {
        return hasAdminPrivileges() || hasStoreAdminPrivileges(storeId);
    }

    private boolean hasAdminPrivileges(Collection<? extends GrantedAuthority> authorities) {
        return authorities.stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(auth -> auth.equals(ROLE_ADMIN)
                        || auth.equals(ROLE_PLATFORM_ADMIN)
                        || auth.equals(ROLE_TENANT_ADMIN));
    }

    private boolean isStoreAdminForVenue(String authority, String venueId) {
        return authority.equals(STORE_PREFIX + venueId + STORE_ADMIN_SUFFIX)
                || authority.equals(STORE_PREFIX + venueId + STORE_STANDARD_SUFFIX)
                || authority.equals(STORE_PREFIX + venueId + TENANT_ADMIN_SUFFIX);
    }
}
