package com.xo.backend.utlis;

import lombok.experimental.UtilityClass;

import java.util.Base64;
import java.util.Random;
import java.util.UUID;

@UtilityClass
public class BookingReferenceGenerator {
    private final Random RANDOM = new Random();
    private final String ALLOWED_CHARS = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"; // Excluding I, O, 1, 0
    private final int MIN_LENGTH = 6;
    private final int MAX_LENGTH = 8;

    public static String generateReferenceNumber() {
        UUID uuid = UUID.randomUUID();
        String encodedUUID = Base64.getUrlEncoder().withoutPadding().encodeToString(uuid.toString().getBytes());
        String reference = encodedUUID.substring(0, 8);
        String cleanReference = reference.replaceAll("[O0I1]", "").toUpperCase();

        return ensureVariableLength(cleanReference);
    }

    private String ensureVariableLength(String reference) {
        StringBuilder result = new StringBuilder(reference);

        // Adjust length if shorter than the minimum length
        while (result.length() < MIN_LENGTH) {
            int randomIndex = RANDOM.nextInt(ALLOWED_CHARS.length());
            result.append(ALLOWED_CHARS.charAt(randomIndex));
        }

        // If longer than the max length, truncate
        if (result.length() > MAX_LENGTH) {
            return result.substring(0, MAX_LENGTH);
        }

        return result.toString();
    }

}
