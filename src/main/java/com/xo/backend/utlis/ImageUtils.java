package com.xo.backend.utlis;

import com.xo.backend.error.exceptions.GeneralException;
import org.springframework.http.HttpStatus;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.awt.image.DataBuffer;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

public class ImageUtils {

    private ImageUtils() {
    }
    //Sample Image processing class used by GO
    private static final Map<String, String> mimeTypeToExtensionMap = new HashMap<>();

    static {
        mimeTypeToExtensionMap.put("image/jpeg", ".jpg");
        mimeTypeToExtensionMap.put("image/png", ".png");
        mimeTypeToExtensionMap.put("image/gif", ".gif");
        mimeTypeToExtensionMap.put("image/bmp", ".bmp");
        mimeTypeToExtensionMap.put("image/tiff", ".tiff");
        mimeTypeToExtensionMap.put("video/mp4", ".mp4");
        mimeTypeToExtensionMap.put("video/quicktime", ".mov");
    }

    public static String getFileExtension(String contentType) {
        return mimeTypeToExtensionMap.getOrDefault(contentType, "");
    }

    public static String getImageName(String imagePath) {
        int lastIndex = imagePath.lastIndexOf(".");
        if (lastIndex != -1) {
            return imagePath.substring(imagePath.lastIndexOf(File.separator) + 1, lastIndex);
        }
        throw new GeneralException("Invalid image path", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public static String getImageType(String imagePath) {
        int lastIndex = imagePath.lastIndexOf(".");
        if (lastIndex != -1) {
            return imagePath.substring(lastIndex + 1);
        }
        throw new GeneralException("Invalid image path",HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private static double getFactor(long imageSizeInBytes) {
        double factor = 1.0;
        if (imageSizeInBytes > 104857600L) {
            factor = 0.125;
        } else if (imageSizeInBytes > 10485760L) {
            factor = 0.25;
        } else if (imageSizeInBytes > 1048576L) {
            factor = 0.50;
        }
        return factor;
    }

    public static BufferedImage resize(BufferedImage image, int newWidth, int newHeight) {
        int currentWidth = image.getWidth();
        int currentHeight = image.getHeight();
        double widthRatio = (currentWidth * 1.0) / newWidth;
        double heightRatio = (currentHeight * 1.0) / newHeight;
        if (widthRatio > heightRatio) {
            newWidth = (int) Math.ceil(currentWidth / heightRatio);
        } else {
            newHeight = (int) Math.ceil(currentHeight / widthRatio);
        }
        BufferedImage resized = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = resized.createGraphics();
        graphics.drawImage(
                image.getScaledInstance(newWidth, newHeight, Image.SCALE_SMOOTH), 0, 0, null);
        graphics.dispose();
        return resized;
    }

    public static BufferedImage resize(BufferedImage image) {
        DataBuffer dataBuffer = image.getData().getDataBuffer();
        long sizeBytes = (dataBuffer.getSize()) * 4L;
        double resizeFactor = getFactor(sizeBytes);
        BufferedImage resized =
                new BufferedImage(
                        (int) (image.getWidth() * resizeFactor),
                        (int) (image.getHeight() * resizeFactor),
                        BufferedImage.TYPE_INT_ARGB);
        AffineTransformOp affineTransformOp =
                new AffineTransformOp(
                        AffineTransform.getScaleInstance(resizeFactor, resizeFactor),
                        AffineTransformOp.TYPE_BICUBIC);
        affineTransformOp.filter(image, resized);
        return resized;
    }

    public static BufferedImage resizeImage(BufferedImage image, int width, int height) {
        double aspectRatio = (double) image.getWidth(null) / (double) image.getHeight(null);
        int imgWidth = image.getWidth();
        int imgHeight = image.getHeight();
        int newWidth = width;
        int newHeight = height;

        if (imgWidth >= imgHeight) {
            if (imgWidth > width) {
                newHeight = (int) (width / aspectRatio);
            }
        } else {
            if (imgHeight > height) {
                newWidth = (int) (height / aspectRatio);
            }
        }
        BufferedImage bufferedImage =
                new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics2D = bufferedImage.createGraphics();
        graphics2D.setComposite(AlphaComposite.Src);
        graphics2D.setRenderingHint(
                RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        graphics2D.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        graphics2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics2D.drawImage(image, 0, 0, newWidth, newHeight, null);
        graphics2D.dispose();
        return bufferedImage;
    }

    private static float getCompressionFactor(long fileSize) {
        float factor = 0.8f;
        if (fileSize > (10 * 1024 * 1024)) {
            factor = 0.2f;
        } else if (fileSize > (5 * 1024 * 1024)) {
            factor = 0.25f;
        } else if (fileSize > (3 * 1024 * 1024)) {
            factor = 0.35f;
        } else if (fileSize > (1 * 1024 * 1024)) {
            factor = 0.5f;
        } else if (fileSize > (500 * 1024)) {
            factor = 0.75f;
        }
        return factor;
    }

    public static InputStream compressFile(
            BufferedImage image, String imageType, long fileSize, int width, int height)
            throws IOException {
        /* skip compression for gif and avif */
        if (imageType.equalsIgnoreCase("gif") || imageType.equalsIgnoreCase("avif")) {
            return null;
        }

        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            BufferedImage newImage = null;
            /*Resize the image only if it's width > 2 X RecommendedWidth, and it's height > 2X RecommendedHeight*/
            if (image.getWidth() > 2 * width && image.getHeight() > 2 * height) {
                width = 2 * width;
                float scale = image.getHeight() * 1.0F / image.getWidth();
                height = (int) Math.ceil(scale * width);
                newImage = new BufferedImage(width, height, image.getType());
                Graphics2D graphics2D = newImage.createGraphics();
                graphics2D.drawImage(image, 0, 0, width, height, null);

                graphics2D.dispose();
            } else {
                newImage = image;
            }
            ImageWriter writer;
            if (newImage.getColorModel() != null && newImage.getColorModel().hasAlpha()) {
                writer = ImageIO.getImageWritersByFormatName("png").next();
            } else {
                writer = ImageIO.getImageWritersByFormatName("jpeg").next();
            }
            ImageWriteParam param = writer.getDefaultWriteParam();
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(getCompressionFactor(fileSize));

            writer.setOutput(ImageIO.createImageOutputStream(os));
            writer.write(null, new IIOImage(newImage, null, null), param);
            writer.dispose();

            return new ByteArrayInputStream(os.toByteArray());
        }
    }
}
