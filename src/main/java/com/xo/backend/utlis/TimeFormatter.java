package com.xo.backend.utlis;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public interface TimeFormatter {

    /**
     * Formats an Instant into a user-friendly string representation in a specified timezone.
     *
     * <p>Converts the given Instant to the specified timezone and formats it
     * using a pattern that includes date, month, year, time, and AM/PM indicator.</p>
     *
     * @param instant The Instant to be formatted (cannot be null)
     * @param zoneString A valid timezone identifier like "Europe/London" or "Asia/Kolkata"
     * @return A formatted date-time string in the specified timezone
     *
     * @throws java.time.DateTimeException if the zone string is invalid
     * @throws NullPointerException if instant or zoneString is null
     *
     * @example
     * // Returns something like "15 Jan 2025, 03:45 PM"
     * String formattedDate = formatInstantInZone(Instant.now(), "Europe/London");
     */
    static String formatInstantInZone(Instant instant, String zoneString) {

        ZoneId zoneId = ZoneId.of(zoneString);
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);

        // Create a user-friendly formatter
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE dd MMM yyyy 'at' hh:mm a");

        // Format the date
        return formatter.format(zonedDateTime);
    }
}
