package com.xo.backend.service;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingTierEntity;
import com.xo.backend.database.entity.bookings.BookingTierType;
import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.database.repository.entry_pass.EntryPassRepository;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.error.exceptions.EntryPassNotFoundException;
import com.xo.backend.error.exceptions.EventAssociationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EntryPassServiceTest {
    @Mock
    private EntryPassRepository entryPassRepository;
    @Mock
    private EventRepository eventRepository;

    @InjectMocks
    private EntryPassService entryPassService;
    private UUID entryPassId;
    private Integer eventId;
    private String venueId;
    private EntryPassEntity entryPassEntity;
    private BookingEntity bookingEntity;


    @BeforeEach
    void setUp() {
        entryPassId = UUID.randomUUID();
        eventId = 1;
        venueId = "1";

        bookingEntity = new BookingEntity();
        bookingEntity.setId(1);
        bookingEntity.setEventId(eventId);

        BookingTierEntity bookingTierEntity = new BookingTierEntity();
        bookingTierEntity.setType(BookingTierType.RESERVATION);

        entryPassEntity = new EntryPassEntity();
        entryPassEntity.setId(entryPassId);
        entryPassEntity.setBooking(bookingEntity);
        entryPassEntity.setBookingTier(bookingTierEntity);
        when(eventRepository.existsByIdAndVenueId(any(), any())).thenReturn(true);
    }

    @Test
    void testGetEntryPassInfo_EntryPassNotFound() {
        when(entryPassRepository.findById(entryPassId)).thenReturn(Optional.empty());

        Exception exception = assertThrows(EntryPassNotFoundException.class, () -> {
            entryPassService.getEntryPassInfo(venueId, eventId, entryPassId);
        });

        assertEquals("Entry pass not found", exception.getMessage());
    }

    @Test
    void testGetEntryPassInfo_EventAssociationMismatch() {
        bookingEntity.setEventId(2);

        when(entryPassRepository.findById(entryPassId)).thenReturn(Optional.of(entryPassEntity));

        Exception exception = assertThrows(EventAssociationException.class, () -> {
            entryPassService.getEntryPassInfo(venueId, eventId, entryPassId);
        });

        assertEquals("Entry pass does not belong to the specified event", exception.getMessage());
    }

    @Test
    void testConsumeEntryPass_EntryPassNotFound() {
        when(entryPassRepository.findById(entryPassId)).thenReturn(Optional.empty());

        Exception exception = assertThrows(EntryPassNotFoundException.class, () -> {
            entryPassService.consumeEntryPass(venueId, eventId, entryPassId);
        });

        assertEquals("Entry pass not found", exception.getMessage());
    }

    @Test
    void testConsumeEntryPass_EventAssociationMismatch() {
        bookingEntity.setEventId(2);

        when(entryPassRepository.findById(entryPassId)).thenReturn(Optional.of(entryPassEntity));

        Exception exception = assertThrows(EventAssociationException.class, () -> {
            entryPassService.consumeEntryPass(venueId, eventId, entryPassId);
        });
        assertEquals("Entry pass does not belong to the specified event", exception.getMessage());
    }

    @Test
    void testConsumeEntryPass_InvalidEventAssociation() {
        entryPassId = UUID.randomUUID();
        eventId = 999;
        EntryPassEntity entryPass = new EntryPassEntity();
        entryPass.setId(entryPassId);

        BookingEntity booking = new BookingEntity();
        booking.setEventId(1);
        entryPass.setBooking(booking);

        when(entryPassRepository.findById(entryPassId)).thenReturn(Optional.of(entryPass));

        assertThrows(EventAssociationException.class, () -> {
            entryPassService.consumeEntryPass(venueId, eventId, entryPassId);
        });
    }
}
