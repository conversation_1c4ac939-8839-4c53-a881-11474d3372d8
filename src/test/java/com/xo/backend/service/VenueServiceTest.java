package com.xo.backend.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.database.service.VenueService;
import com.xo.backend.mappers.EventDetailsMapper;
import com.xo.backend.model.dto.go.Address;
import com.xo.backend.model.dto.go.Country;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.responses.VenueDetailsResponseDTO;
import com.xo.backend.model.dto.responses.venueresponse.Coordinates;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VenueServiceTest {

    private static final String VENUE_ID = "1";
    private static final String VENUE_NAME = "Test Venue";
    private static final String VENUE_DESCRIPTION = "A great venue for events";
    private static final String PHONE_NUMBER = "+123456789";
    private static final String ADDRESS_LINE1 = "123 Test St.";
    private static final String CITY = "Test City";
    private static final String POST_CODE = "12345";
    private static final String COUNTRY_NAME = "Testland";
    private static final String COUNTRY_CODE = "TL";
    private static final String COUNTRY_PHONE_CODE = "+123";
    private static final String DEFAULT_TIME_ZONE = "America/Los_Angeles";

    @Mock
    private VenueInfoService venueInfoService;
    @Mock
    private EventRepository eventRepository;
    @Mock
    private EventDetailsMapper eventMapper;
    @InjectMocks
    private VenueService venueService;


    private static Map<String, String> createSettings() {
        Map<String, String> settings = new HashMap<>();
        settings.put("LOGO", "logo_url");
        settings.put("IMAGE", "image_url");
        return settings;
    }
    @Test
    void shouldHandleEmptySchedules() {
        // Arrange
        VenueDTO venueWithInvalidCoordinates = VenueDTO.builder()
                .id(1)
                .name(VENUE_NAME)
                .description(VENUE_DESCRIPTION)
                .address(new Address(ADDRESS_LINE1, null, POST_CODE, CITY,
                        new Country(COUNTRY_NAME, COUNTRY_CODE, COUNTRY_PHONE_CODE, DEFAULT_TIME_ZONE)))
                .phoneNumber(PHONE_NUMBER)
                .settings(createSettings())
                .coordinates(new Coordinates(null, null))
                .build();

        when(venueInfoService.getVenueDetails(VENUE_ID)).thenReturn(venueWithInvalidCoordinates);

        // Act & Assert
        VenueDetailsResponseDTO venueDetails = venueService.getVenueDetails(VENUE_ID);

        // Assert
        assertEquals(venueWithInvalidCoordinates.name(), venueDetails.name());
        assertEquals(Collections.emptyList(), venueDetails.operatingTimes());
    }

    @Test
    void shouldThrowExceptionIfVenueDetailsAreNotFound() {
        when(venueInfoService.getVenueDetails(VENUE_ID)).thenThrow(new RuntimeException("Venue not found"));

        assertThrows(RuntimeException.class, () -> venueService.getVenueDetails(VENUE_ID));
    }
}
