package com.xo.backend.service;

import com.xo.backend.client.email.EmailService;
import com.xo.backend.client.user.UserInfoService;
import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.database.service.EventAttributeService;
import com.xo.backend.error.exceptions.BookingNotFoundException;
import com.xo.backend.error.exceptions.EventNotFoundException;
import com.xo.backend.error.exceptions.InvalidBookingStatusException;
import com.xo.backend.mappers.BookingMapper;
import com.xo.backend.model.dto.BookingDTO;
import com.xo.backend.model.dto.BookingItemDTO;
import com.xo.backend.model.dto.go.GoUserProfileResponseDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.backend.model.dto.request.booking.AddItemsToBookingRequest;
import com.xo.backend.model.dto.request.booking.BookingStatusUpdateResponseDTO;
import com.xo.backend.model.dto.request.booking.CreateBookingRequest;
import com.xo.backend.model.dto.responses.BookingListResponse;
import com.xo.backend.model.dto.responses.CreateBookingResponseDTO;
import com.xo.backend.utlis.BookingHelper;
import com.xo.backend.utlis.SecurityContextUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.xo.backend.database.entity.bookings.BookingStatus.DRAFT;
import static com.xo.backend.utlis.Constants.BANNER;
import static com.xo.backend.utlis.Constants.TITLE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Execution(ExecutionMode.CONCURRENT)
class BookingServiceTest {
    private static final int BOOKING_ID = 1;
    private static final BookingDTO bookingDTO = BookingDTO.builder()
            .bookingStatus(BookingStatus.DRAFT).bookingId(1).build();

    private static final List<BookingItemDTO> BOOKING_ITEM_LIST = List.of(
            BookingItemDTO.builder().bookingTierId(1).numOfPeople(1).quantity(1).build(),
            BookingItemDTO.builder().bookingTierId(2).numOfPeople(2).quantity(1).build()
    );
    @Mock
    private BookingRepository bookingRepository;
    @Mock
    private BookingMapper bookingMapper;
    @Mock
    private UserInfoService userInfoService;
    @Mock
    private VenueInfoService venueInfoService;
    @Mock
    private EventRepository eventRepository;
    @Mock
    private EventAttributeService eventAttributeService;
    @Mock
    private EmailService emailService;
    @Mock
    private EntryPassService entryPassService;
    @Mock
    private BookingHelper bookingHelper;
    @InjectMocks
    private BookingService bookingService;

    private MockedStatic<SecurityContextUtil> mockedSecurityContext;

    private static final Integer USER_ID = 1;
    private static final Integer EVENT_ID = 100;
    private static final String VENUE_ID = "200";
    private static final String EVENT_TITLE = "Test Event";
    private static final String EVENT_BANNER = "test-image.jpg";
    private static final String VENUE_NAME = "Test Venue";
    private static final CreateBookingRequest CREATE_BOOKING_REQUEST = CreateBookingRequest.builder().userId(USER_ID).eventId(EVENT_ID).build();
    private static final AddItemsToBookingRequest ADD_ITEMS_TO_BOOKING_REQUEST = AddItemsToBookingRequest.builder().bookingItems(BOOKING_ITEM_LIST).build();

    @BeforeEach
    void setUp() {
        mockedSecurityContext = mockStatic(SecurityContextUtil.class);
        mockedSecurityContext.when(SecurityContextUtil::getCurrentUserId).thenReturn(USER_ID);
    }

    @AfterEach
    void tearDown() {
        mockedSecurityContext.close();
    }

    //create new booking
    @Test
    void shouldCreateADraftBooking() {
        //given
        lenient().when(bookingRepository.findBookingEntityByOwnerIdAndEventId(USER_ID, EVENT_ID)).thenReturn(Optional.empty());

        BookingEntity draftBooking = BookingEntity.builder()
                .id(1)
                .status(DRAFT)
                .build();
        lenient().when(bookingRepository.save(any(BookingEntity.class))).thenReturn(draftBooking);
        lenient().when(bookingMapper.mapToBookingDTO(draftBooking)).thenReturn(bookingDTO);
        when(userInfoService.getUserProfile()).thenReturn(GoUserProfileResponseDTO.builder().id(1L).firstName("John").lastName("Doe").phoneNumber("96231241").email("<EMAIL>").build());

        //when
        CreateBookingResponseDTO result = bookingService.createNewBooking(CREATE_BOOKING_REQUEST);

        //then
        assertEquals(1, result.booking().bookingId());
    }

    // add items to booking
    @Test
    void shouldThrowExceptionIfNoBookingIsFoundOnTryingToAddItems() {
        //given
        when(bookingRepository.findById(BOOKING_ID)).thenReturn(Optional.empty());

        //when
        assertThrows(BookingNotFoundException.class,
                () -> bookingService.addItemsToBooking(BOOKING_ID, ADD_ITEMS_TO_BOOKING_REQUEST));

        //then
        verifyNoInteractions(userInfoService);
        verifyNoMoreInteractions(bookingRepository);
    }

    @Test
    void shouldThrowExceptionIfBookingNotFound() {
        //given
        when(bookingRepository.findById(BOOKING_ID)).thenReturn(Optional.empty());

        //when
        assertThrows(BookingNotFoundException.class,
                () -> bookingService.addItemsToBooking(BOOKING_ID, ADD_ITEMS_TO_BOOKING_REQUEST));
    }

    @Test
    void shouldThrowExceptionIfBookingIsNotDraft() {
        //given
        when(bookingRepository.findById(BOOKING_ID)).thenReturn(Optional.of(BookingEntity.builder().id(BOOKING_ID).ownerId(USER_ID).eventId(EVENT_ID).status(BookingStatus.APPROVED).build()));

        //when
        assertThrows(InvalidBookingStatusException.class,
                () -> bookingService.addItemsToBooking(BOOKING_ID, ADD_ITEMS_TO_BOOKING_REQUEST));
    }

    @Test
    void testFinalizeBooking_Success_NoPaymentRequired() {
        Integer bookingId = 1;
        BookingEntity booking = new BookingEntity();
        booking.setStatus(DRAFT);
        BookingTierEntity bookingTier = new BookingTierEntity();
        bookingTier.setRequireConfirmation(BookingConfirmationRequirementEnum.NO);
        bookingTier.setPayOnlinePrice(BigDecimal.ZERO);
        BookingItemEntity bookingItem = new BookingItemEntity();
        bookingItem.setBookingTier(bookingTier);
        booking.setBookingItems(Collections.singletonList(bookingItem));
        booking.setEventId(1);
        booking.setNetAmount(BigDecimal.ZERO);
        booking.setTotalFee(BigDecimal.ZERO);
        booking.setTotalAmount(BigDecimal.ZERO);
        booking.setRequireConfirmation(BookingConfirmationRequirementEnum.NO);

        bookingTier.setType(BookingTierType.TICKET);
        when(bookingRepository.findById(bookingId)).thenReturn(Optional.of(booking));
        doNothing().when(entryPassService).createEntryPassesAndUpdateAvailability(any());
        doNothing().when(emailService).sendBookingEmail(any());

        when(bookingHelper.isBookingConfirmationRequired(any(BookingConfirmationRequirementEnum.class))).thenReturn(false);
        BookingStatusUpdateResponseDTO result = bookingService.finalizeBooking(bookingId);

        assertEquals(BookingStatus.CONFIRMED, result.status());
        verify(bookingRepository).save(booking);
        verify(entryPassService).createEntryPassesAndUpdateAvailability(booking);

    }

    @Test
    void testFinalizeBooking_BookingNotFound() {

        Integer bookingId = 1;
        when(bookingRepository.findById(bookingId)).thenReturn(Optional.empty());

        assertThrows(BookingNotFoundException.class, () -> bookingService.finalizeBooking(bookingId));
    }


    @Test
    void testFinalizeBooking_InvalidBookingStatus() {

        Integer bookingId = 1;
        BookingEntity booking = new BookingEntity();
        booking.setStatus(BookingStatus.CONFIRMED);

        when(bookingRepository.findById(bookingId)).thenReturn(Optional.of(booking));

        assertThrows(InvalidBookingStatusException.class, () -> bookingService.finalizeBooking(bookingId));
    }

    @Test
    void testGetBookingDetailsBookingNotFound() {
        when(bookingRepository.findById(1)).thenReturn(Optional.empty());

        assertThrows(BookingNotFoundException.class, () -> bookingService.getBookingDetails(1));
    }

    @Test
    void getBookingDetails_ShouldThrowBookingNotFoundException() {
        // Arrange
        int bookingId = 1;
        when(bookingRepository.findById(bookingId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(BookingNotFoundException.class, () -> bookingService.getBookingDetails(bookingId));
    }


    @Test
    void getAllBookingsForUser_ShouldReturnPageOfBookings() {
        when(venueInfoService.getVenuesOverview(anySet())).thenReturn(Map.of(Long.valueOf(VENUE_ID), VenueOverviewDTO.builder().name(VENUE_NAME).storeId(Long.valueOf(VENUE_ID)).build()));
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        BookingEntity booking = createTestBooking();
        List<BookingEntity> bookingList = List.of(booking);
        Page<BookingEntity> bookingPage = new PageImpl<>(bookingList, pageable, 1);
        EventEntity event = createTestEvent(false); // not past event

        // Mock attribute service responses
        EventAttributeEntity titleAttribute = new EventAttributeEntity();
        titleAttribute.setAttributeValue(EVENT_TITLE);
        EventAttributeEntity imageAttribute = new EventAttributeEntity();
        imageAttribute.setAttributeValue(EVENT_BANNER);

        // Mock repository and service responses
        when(bookingRepository.findBookingEntitiesByOwnerIdAndStatusIn(
                eq(USER_ID),
                anyList(),
                eq(pageable)
        )).thenReturn(bookingPage);

        when(eventRepository.findById(EVENT_ID)).thenReturn(Optional.of(event));
        when(eventAttributeService.getEventAttributeByName(event.getEventAttributes(), TITLE))
                .thenReturn(titleAttribute);
        when(eventAttributeService.getEventAttributeByName(event.getEventAttributes(), BANNER))
                .thenReturn(imageAttribute);

        // Act
        Page<BookingListResponse> result = bookingService.getAllBookingsForUser(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());

        BookingListResponse resultDTO = result.getContent().getFirst();
        assertEquals(booking.getId(), resultDTO.bookingId());
        assertEquals(EVENT_TITLE, resultDTO.eventTitle());
        assertEquals(EVENT_BANNER, resultDTO.eventBanner());
        assertEquals(VENUE_NAME, resultDTO.venueName());
        assertEquals(BookingStatus.CONFIRMED.toString(), resultDTO.status());

        // Verify interactions
        verify(bookingRepository).findBookingEntitiesByOwnerIdAndStatusIn(
                eq(USER_ID),
                anyList(),
                eq(pageable)
        );
        verify(eventRepository).findById(EVENT_ID);
        verify(eventAttributeService).getEventAttributeByName(event.getEventAttributes(), TITLE);
        verify(eventAttributeService).getEventAttributeByName(event.getEventAttributes(), BANNER);
    }

    @Test
    void getAllBookingsForUser_WithPastEvent_ShouldReturnPastStatus() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        BookingEntity booking = createTestBooking();
        List<BookingEntity> bookingList = List.of(booking);
        Page<BookingEntity> bookingPage = new PageImpl<>(bookingList, pageable, 1);
        EventEntity event = createTestEvent(true); // past event

        // Mock attribute service responses
        EventAttributeEntity titleAttribute = new EventAttributeEntity();
        titleAttribute.setAttributeValue(EVENT_TITLE);
        EventAttributeEntity imageAttribute = new EventAttributeEntity();
        imageAttribute.setAttributeValue(EVENT_BANNER);

        when(bookingRepository.findBookingEntitiesByOwnerIdAndStatusIn(
                eq(USER_ID),
                anyList(),
                eq(pageable)
        )).thenReturn(bookingPage);

        when(eventRepository.findById(EVENT_ID)).thenReturn(Optional.of(event));
        when(eventAttributeService.getEventAttributeByName(event.getEventAttributes(), TITLE))
                .thenReturn(titleAttribute);
        when(eventAttributeService.getEventAttributeByName(event.getEventAttributes(), BANNER))
                .thenReturn(imageAttribute);

        // Act
        Page<BookingListResponse> result = bookingService.getAllBookingsForUser(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(BookingStatus.PAST.toString(), result.getContent().getFirst().status());
    }

    @Test
    void getAllBookingsForUser_WhenEventNotFound_ShouldThrowException() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        BookingEntity booking = createTestBooking();

        List<BookingEntity> bookingList = List.of(booking);
        Page<BookingEntity> bookingPage = new PageImpl<>(bookingList, pageable, 1);

        when(bookingRepository.findBookingEntitiesByOwnerIdAndStatusIn(
                eq(USER_ID),
                anyList(),
                eq(pageable)
        )).thenReturn(bookingPage);

        when(eventRepository.findById(EVENT_ID)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(EventNotFoundException.class, () ->
                bookingService.getAllBookingsForUser(pageable)
        );
    }

    private BookingEntity createTestBooking() {
        return BookingEntity.builder()
                .id(1)
                .eventId(EVENT_ID)
                .event(createTestEvent(false))
                .ownerId(USER_ID)
                .bookingName("Test Booking")
                .status(BookingStatus.CONFIRMED)
                .bookingType(BookingType.TICKET)
                .build();
    }

    private EventEntity createTestEvent(boolean isPast) {
        Instant startTime = isPast
                ? Instant.now().minus(2, ChronoUnit.DAYS)
                : Instant.now().plus(1, ChronoUnit.DAYS);
        Instant endTime = isPast
                ? Instant.now().minus(1, ChronoUnit.DAYS)
                : Instant.now().plus(2, ChronoUnit.DAYS);

        EventEntity event = new EventEntity();
        event.setId(EVENT_ID);
        event.setVenueId(VENUE_ID);
        event.setStartDatetime(startTime);
        event.setEndDatetime(endTime);
        event.setEventAttributes(Collections.emptyList());

        return event;
    }


    @Test
    void testGetBookingDetails_BookingNotFound() {
        // Arrange
        int bookingId = 1;
        when(bookingRepository.findById(bookingId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(BookingNotFoundException.class, () -> bookingService.getBookingDetails(bookingId));
        verify(bookingRepository, times(1)).findById(bookingId);
    }

    @Test
    void shouldCreateNewBookingWithCorrectChannel() {
        // Given
        int userId = 123;
        int eventId = 456;

        CreateBookingRequest request = CreateBookingRequest.builder()
                .userId(userId)
                .eventId(eventId)
                .build();

        EventEntity eventEntity = EventEntity.builder()
                .id(eventId)
                .venueId("Test Venue")
                .build();

        BookingDTO mockBookingDTO = BookingDTO.builder()
                .bookingChannel(BookingChannel.USER_APP)
                .build();

        when(eventRepository.findById(eventId)).thenReturn(Optional.of(eventEntity));
        when(bookingRepository.findByOwnerIdAndEventIdAndStatus(userId, eventId, BookingStatus.DRAFT))
                .thenReturn(Optional.empty());
        when(bookingRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        when(bookingMapper.mapToBookingDTO(any())).thenReturn(mockBookingDTO);
        when(userInfoService.getUserProfile()).thenReturn(GoUserProfileResponseDTO.builder().id(1L).firstName("John").lastName("Doe").phoneNumber("96231241").email("<EMAIL>").build());

        // When
        CreateBookingResponseDTO response = bookingService.createNewBooking(request);

        // Then
        assertNotNull(response.booking(), "Booking should not be null in the response");
        assertEquals(BookingChannel.USER_APP, response.booking().bookingChannel());
        verify(bookingRepository).save(argThat(booking -> booking.getBookingChannel() == BookingChannel.USER_APP));
    }


}