package com.xo.backend.service;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.util.AssertionErrors.fail;

@ExtendWith(MockitoExtension.class)
class WorkbookServiceTest {

    @InjectMocks
    private WorkbookService workbookService;

    @Test
    void shouldCreateSingleSheetWorkbookSuccessfully() {
        // Given
        String sheetName = "Test Sheet";
        String[] headers = {"Header 1", "Header 2", "Header 3"};
        List<String[]> data = Arrays.asList(
                new String[]{"Row 1 Cell 1", "Row 1 Cell 2", "Row 1 Cell 3"},
                new String[]{"Row 2 Cell 1", "Row 2 Cell 2", "Row 2 Cell 3"},
                new String[]{"Row 3 Cell 1", "Row 3 Cell 2", "Row 3 Cell 3"}
        );

        // When
        byte[] result = workbookService.createSingleSheetXlsxWorkbook(sheetName, headers, data);

        // Then
        assertNotNull(result);
        assertTrue(result.length > 0, "Excel file should contain data");
    }

    @Test
    void shouldCreateWorkbookWithEmptyData() {
        // Given
        String sheetName = "Empty Data";
        String[] headers = {"Header 1", "Header 2"};
        List<String[]> data = new ArrayList<>();

        // When
        byte[] result = workbookService.createSingleSheetXlsxWorkbook(sheetName, headers, data);

        // Then
        assertNotNull(result);
        assertTrue(result.length > 0, "Excel file should be created even with empty data");
    }

    @Test
    void shouldCreateWorkbookWithEmptyHeaders() {
        // Given
        String sheetName = "Empty Headers";
        String[] headers = {};
        List<String[]> data = List.of(new String[]{"Data 1", "Data 2"}, new String[]{"Data 1", "Data 2"});

        // When
        byte[] result = workbookService.createSingleSheetXlsxWorkbook(sheetName, headers, data);

        // Then
        assertNotNull(result);
        assertTrue(result.length > 0, "Excel file should be created even with empty headers");
    }

    @Test
    void shouldPopulateNullValuesInDataWithEmptyCells() {
        // Given
        String sheetName = "Null Values";
        String[] headers = {"Header 1", "Header 2"};
        List<String[]> data = List.of(
                new String[]{"Value", null},
                new String[]{null, "Value"}
        );

        // When
        byte[] result = workbookService.createSingleSheetXlsxWorkbook(sheetName, headers, data);

        // Then
        try (XSSFWorkbook workbook = new XSSFWorkbook(new ByteArrayInputStream(result))) {
            Sheet sheet = workbook.getSheetAt(0);

            // Verify headers
            Row headerRow = sheet.getRow(0);
            assertEquals("Header 1", headerRow.getCell(0).getStringCellValue());
            assertEquals("Header 2", headerRow.getCell(1).getStringCellValue());

            // Verify first data row
            Row firstDataRow = sheet.getRow(1);
            assertEquals("Value", firstDataRow.getCell(0).getStringCellValue());
            assertEquals("",firstDataRow.getCell(1).getStringCellValue());

            // Verify second data row
            Row secondDataRow = sheet.getRow(2);
            assertEquals("", secondDataRow.getCell(0).getStringCellValue());
            assertEquals("Value", secondDataRow.getCell(1).getStringCellValue());

        } catch (IOException e) {
            fail("Failed to read generated Excel file: " + e.getMessage());
        }
    }
}