package com.xo.backend.mappers;

import com.xo.backend.database.entity.notifications.DeviceTokenEntity;
import com.xo.backend.model.dto.notifications.DeviceTokenResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = DeviceTokenMapperImpl.class)
class DeviceTokenMapperTest {

    @Autowired
    private DeviceTokenMapper deviceTokenMapper;

    @Test
    void shouldMapDeviceTokenEntityToResponse() {
        // Given
        Instant now = Instant.now();
        DeviceTokenEntity entity = DeviceTokenEntity.builder()
                .userId(1)
                .deviceToken("device-token-123")
                .deviceOs("iOS")
                .deviceName("iPhone 13")
                .tokenExpiry(now.plusSeconds(3600))
                .isActive(true)
                .pushEnabled(true)
                .lastNotificationSent(now.minusSeconds(3600))
                .build();
        
        // Set audit fields
        entity.setCreatedAt(now.minusSeconds(7200));
        entity.setUpdatedAt(now);

        // When
        DeviceTokenResponse response = deviceTokenMapper.toDto(entity);

        // Then
        assertNotNull(response);
        assertEquals(entity.getUserId(), response.userId());
        assertEquals(entity.getDeviceToken(), response.deviceToken());
        assertEquals(entity.getDeviceOs(), response.deviceOs());
        assertEquals(entity.getDeviceName(), response.deviceName());
        assertEquals(entity.getTokenExpiry(), response.tokenExpiry().toInstant());
        assertEquals(entity.getIsActive(), response.isActive());
        assertEquals(entity.getPushEnabled(), response.pushEnabled());
        assertEquals(entity.getLastNotificationSent(), response.lastNotificationSent().toInstant());
        assertEquals(entity.getCreatedAt(), response.createdAt().toInstant());
        assertEquals(entity.getUpdatedAt(), response.updatedAt().toInstant());
    }
}