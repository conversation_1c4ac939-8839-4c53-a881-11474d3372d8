package com.xo.backend.mappers;

import com.xo.backend.database.entity.AttributeEntity;
import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingStatus;
import com.xo.backend.database.entity.bookings.BookingType;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.entity.events.EventStatus;
import com.xo.backend.model.dto.AddressDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.responses.BookingListResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;

import static com.xo.backend.utlis.Constants.BANNER;
import static com.xo.backend.utlis.Constants.TITLE;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class BookingListMapperTest {

    @Spy
    @InjectMocks
    private BookingListMapperImpl mapper;

    @Test
    void toBookingListResponse_WithValidData_ShouldMapCorrectly() {
        // Arrange
        Instant now = Instant.now().truncatedTo(ChronoUnit.SECONDS);
        BookingEntity booking = createBookingEntity(now);
        AddressDTO addressDTO = createAddressDTO();
        boolean hasEntryPasses = true;

        // Act
        BookingListResponse response = mapper.toBookingListResponse(booking, addressDTO, hasEntryPasses, VenueDTO.builder().timeZone("us").build());

        // Assert
        assertNotNull(response);
        assertEquals(1, response.bookingId());
        assertEquals(100, response.eventId());
        assertEquals(EventStatus.PUBLISHED, response.eventStatus());
        assertEquals("Test Event", response.eventTitle());
        assertEquals("banner.jpg", response.eventBanner());
        assertEquals("Test Venue", response.venueName());
        assertEquals(BookingType.TICKET, response.bookingType());
        assertEquals(BookingStatus.CONFIRMED.toString(), response.status());
        assertEquals("Test Booking", response.bookingName());
        assertTrue(response.hasEntryPasses());
        
        // Verify eventDateTime
        OffsetDateTime expectedDateTime = OffsetDateTime.ofInstant(now, ZoneOffset.UTC);
        assertEquals(expectedDateTime, response.eventDateTime());
    }

    @Test
    void toBookingListResponse_WithNullEvent_ShouldMapWithoutEventAttributes() {
        // Arrange
        BookingEntity booking = BookingEntity.builder()
                .id(1)
                .bookingType(BookingType.TICKET)
                .status(BookingStatus.CONFIRMED)
                .bookingName("Test Booking")
                .build();
        AddressDTO addressDTO = createAddressDTO();

        // Act
        BookingListResponse response = mapper.toBookingListResponse(booking, addressDTO, false, VenueDTO.builder().timeZone("us").build());

        // Assert
        assertNotNull(response);
        assertNull(response.eventTitle());
        assertNull(response.eventBanner());
        assertNull(response.eventDateTime());
    }

    @Test
    void toBookingListResponse_WithNullStartDateTime_ShouldMapWithNullEventDateTime() {
        // Arrange
        BookingEntity booking = createBookingEntity(null);
        AddressDTO addressDTO = createAddressDTO();

        // Act
        BookingListResponse response = mapper.toBookingListResponse(booking, addressDTO, false, VenueDTO.builder().timeZone("us").build());

        // Assert
        assertNotNull(response);
        assertNull(response.eventDateTime());
    }

    private BookingEntity createBookingEntity(Instant startTime) {
        EventEntity event = new EventEntity();
        event.setId(100);
        event.setStatus(EventStatus.PUBLISHED);
        event.setStartDatetime(startTime);
        
        AttributeEntity titleAttr = new AttributeEntity();
        titleAttr.setName(TITLE);
        AttributeEntity bannerAttr = new AttributeEntity();
        bannerAttr.setName(BANNER);

        EventAttributeEntity titleAttribute = new EventAttributeEntity();
        titleAttribute.setAttribute(titleAttr);
        titleAttribute.setAttributeValue("Test Event");

        EventAttributeEntity bannerAttribute = new EventAttributeEntity();
        bannerAttribute.setAttribute(bannerAttr);
        bannerAttribute.setAttributeValue("banner.jpg");

        event.setEventAttributes(Arrays.asList(titleAttribute, bannerAttribute));

        return BookingEntity.builder()
                .id(1)
                .eventId(100)
                .event(event)
                .bookingType(BookingType.TICKET)
                .status(BookingStatus.CONFIRMED)
                .bookingName("Test Booking")
                .build();
    }

    private AddressDTO createAddressDTO() {
        return AddressDTO.builder()
                .addressName("Test Venue")
                .build();
    }
}