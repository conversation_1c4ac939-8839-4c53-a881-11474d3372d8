package com.xo.backend.database.service;

import com.xo.backend.database.entity.LanguageEntity;
import com.xo.backend.database.repository.LanguageRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LanguageServiceTest {
    @Mock
    private LanguageRepository languageRepository;

    @InjectMocks
    private LanguageService languageService;

    @Test
    void shouldGetLanguageEntityMapForTwoLanguages() {
        //given
        List<String> languageNames = List.of("English", "Spanish");
        when(languageRepository.findAllByNameIn(languageNames)).thenReturn(List.of(
                LanguageEntity.builder().name("English").build(),
                LanguageEntity.builder().name("Spanish").build()
        ));

        //when
        Map<String, LanguageEntity> languageEntityMap = languageService.getLanguageEntityMap(languageNames);

        //then
        assertEquals(2, languageEntityMap.size());
        assertEquals("English", languageEntityMap.get("English").getName());
        assertEquals("Spanish", languageEntityMap.get("Spanish").getName());
    }

    @Test
    void shouldReturnEmptyMapIfNotInDatabase() {
        //given
        List<String> languageNames = List.of("English", "Spanish");
        when(languageRepository.findAllByNameIn(languageNames)).thenReturn(Collections.emptyList());

        //when
        Map<String, LanguageEntity> languageEntityMap = languageService.getLanguageEntityMap(languageNames);

        //then
        assertEquals(0, languageEntityMap.size());
    }
}