package com.xo.backend.database.service;

import com.xo.backend.database.entity.AttributeEntity;
import com.xo.backend.database.repository.AttributeRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AttributeServiceTest {

    @Mock
    private AttributeRepository attributeRepository;

    @InjectMocks
    private AttributeService attributeService;

    @Test
    void shouldReturnAttributeMapWith2Attributes() {

        //given
        when(attributeRepository.findAllByNameIn(List.of("tier-name", "description")))
                .thenReturn(List.of(
                        AttributeEntity.builder().name("tier-name").build(),
                        AttributeEntity.builder().name("description").build()
                ));
        //when
        Map<String, AttributeEntity> attributeMap = attributeService.getAttributeEntityMap(List.of("tier-name", "description"));

        //then
        assertEquals(2, attributeMap.size());
    }

    @Test
    void shouldReturnEmptyMapIfNotMatchingAttributesFound() {

        //given
        when(attributeRepository.findAllByNameIn(List.of("tier-name", "description"))).thenReturn(Collections.emptyList());
        //when
        Map<String, AttributeEntity> attributeMap = attributeService.getAttributeEntityMap(List.of("tier-name", "description"));

        //then
        assertEquals(0, attributeMap.size());
    }

    @Test
    void shouldReturnAttributeEntityGivenName() {

        //given
        Map<String, AttributeEntity> attributeMap = Map.of("tier-name", AttributeEntity.builder().name("tier-name").build(),
                "description", AttributeEntity.builder().name("description").build());
        //when
        AttributeEntity attribute = attributeService.getAttributeByName(attributeMap, "tier-name");

        //then
        assertEquals("tier-name", attribute.getName());
    }

    @Test
    void shouldThrowExceptionIfNoAttributeNameFoundInMap() {

        //given
        Map<String, AttributeEntity> attributeMap = Map.of("tier-name", AttributeEntity.builder().name("tierName").build(),
                "description", AttributeEntity.builder().name("description").build());

        //then
        assertThrows(IllegalArgumentException.class, () -> attributeService.getAttributeByName(attributeMap, "wrongName"));
    }

}