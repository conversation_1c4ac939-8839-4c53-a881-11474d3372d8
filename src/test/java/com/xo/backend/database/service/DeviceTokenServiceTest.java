package com.xo.backend.database.service;

import com.xo.backend.database.entity.notifications.DeviceTokenEntity;
import com.xo.backend.database.entity.notifications.DeviceTokenId;
import com.xo.backend.database.repository.notifications.DeviceTokenRepository;
import com.xo.backend.error.exceptions.ResourceNotFoundException;
import com.xo.backend.mappers.DeviceTokenMapper;
import com.xo.backend.model.dto.notifications.DeviceTokenResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeviceTokenServiceTest {

    @Mock
    private DeviceTokenRepository deviceTokenRepository;

    @Mock
    private DeviceTokenMapper deviceTokenMapper;

    @InjectMocks
    private DeviceTokenService deviceTokenService;

    private static final Integer USER_ID = 123;
    private static final String DEVICE_TOKEN = "test-device-token";
    private static final String DEVICE_OS = "iOS";
    private static final String DEVICE_NAME = "iPhone 14";
    private static final OffsetDateTime TOKEN_EXPIRY = OffsetDateTime.now().plusDays(30);

    private DeviceTokenEntity deviceTokenEntity;
    private DeviceTokenResponse deviceTokenResponse;

    @BeforeEach
    void setUp() {
        deviceTokenEntity = new DeviceTokenEntity();
        deviceTokenEntity.setUserId(USER_ID);
        deviceTokenEntity.setDeviceToken(DEVICE_TOKEN);
        deviceTokenEntity.setDeviceOs(DEVICE_OS);
        deviceTokenEntity.setDeviceName(DEVICE_NAME);
        deviceTokenEntity.setTokenExpiry(TOKEN_EXPIRY.toInstant());
        deviceTokenEntity.setIsActive(true);
        deviceTokenEntity.setPushEnabled(true);

        deviceTokenResponse = DeviceTokenResponse.builder()
                .userId(USER_ID)
                .deviceToken(DEVICE_TOKEN)
                .deviceOs(DEVICE_OS)
                .deviceName(DEVICE_NAME)
                .tokenExpiry(TOKEN_EXPIRY)
                .isActive(true)
                .pushEnabled(true)
                .build();
    }

    @Test
    void registerOrUpdateDeviceToken_WhenNewToken_ShouldCreateNewEntry() {
        // Given
        when(deviceTokenRepository.findById(any(DeviceTokenId.class))).thenReturn(Optional.empty());
        when(deviceTokenRepository.save(any(DeviceTokenEntity.class))).thenReturn(deviceTokenEntity);
        when(deviceTokenMapper.toDto(any(DeviceTokenEntity.class))).thenReturn(deviceTokenResponse);

        // When
        DeviceTokenResponse response = deviceTokenService.registerOrUpdateDeviceToken(
                USER_ID, DEVICE_TOKEN, DEVICE_OS, DEVICE_NAME, TOKEN_EXPIRY);

        // Then
        assertNotNull(response);
        assertEquals(USER_ID, response.userId());
        assertEquals(DEVICE_TOKEN, response.deviceToken());
        assertEquals(DEVICE_OS, response.deviceOs());
        assertEquals(DEVICE_NAME, response.deviceName());
        assertTrue(response.isActive());
        assertTrue(response.pushEnabled());
        verify(deviceTokenRepository).save(any(DeviceTokenEntity.class));
    }

    @Test
    void registerOrUpdateDeviceToken_WhenExistingToken_ShouldUpdateEntry() {
        // Given
        when(deviceTokenRepository.findById(any(DeviceTokenId.class))).thenReturn(Optional.of(deviceTokenEntity));
        when(deviceTokenRepository.save(any(DeviceTokenEntity.class))).thenReturn(deviceTokenEntity);
        when(deviceTokenMapper.toDto(any(DeviceTokenEntity.class))).thenReturn(deviceTokenResponse);

        String newDeviceOs = "Android";
        String newDeviceName = "Pixel 7";

        // When
        DeviceTokenResponse response = deviceTokenService.registerOrUpdateDeviceToken(
                USER_ID, DEVICE_TOKEN, newDeviceOs, newDeviceName, TOKEN_EXPIRY);

        // Then
        assertNotNull(response);
        verify(deviceTokenRepository).save(argThat(entity -> 
            entity.getDeviceOs().equals(newDeviceOs) &&
            entity.getDeviceName().equals(newDeviceName)
        ));
    }

    @Test
    void updateTokenActiveStatus_WhenTokenExists_ShouldUpdateStatus() {
        // Given
        when(deviceTokenRepository.findById(any(DeviceTokenId.class))).thenReturn(Optional.of(deviceTokenEntity));
        when(deviceTokenRepository.save(any(DeviceTokenEntity.class))).thenReturn(deviceTokenEntity);
        when(deviceTokenMapper.toDto(any(DeviceTokenEntity.class))).thenReturn(deviceTokenResponse);

        // When
        DeviceTokenResponse response = deviceTokenService.updateTokenActiveStatus(USER_ID, DEVICE_TOKEN, false);

        // Then
        assertNotNull(response);
        verify(deviceTokenRepository).save(argThat(entity -> 
            !entity.getIsActive() && 
            !entity.getPushEnabled()
        ));
    }

    @Test
    void updateTokenActiveStatus_WhenTokenNotFound_ShouldThrowException() {
        // Given
        when(deviceTokenRepository.findById(any(DeviceTokenId.class))).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            deviceTokenService.updateTokenActiveStatus(USER_ID, DEVICE_TOKEN, true)
        );
    }

    @Test
    void updateTokenPushEnabled_WhenTokenActiveAndExists_ShouldUpdatePushStatus() {
        // Given
        when(deviceTokenRepository.findById(any(DeviceTokenId.class))).thenReturn(Optional.of(deviceTokenEntity));
        when(deviceTokenRepository.save(any(DeviceTokenEntity.class))).thenReturn(deviceTokenEntity);
        when(deviceTokenMapper.toDto(any(DeviceTokenEntity.class))).thenReturn(deviceTokenResponse);

        // When
        DeviceTokenResponse response = deviceTokenService.updateTokenPushEnabled(USER_ID, DEVICE_TOKEN, false);

        // Then
        assertNotNull(response);
        verify(deviceTokenRepository).save(argThat(entity -> !entity.getPushEnabled()));
    }

    @Test
    void updateTokenPushEnabled_WhenTokenInactive_ShouldThrowException() {
        // Given
        deviceTokenEntity.setIsActive(false);
        when(deviceTokenRepository.findById(any(DeviceTokenId.class))).thenReturn(Optional.of(deviceTokenEntity));

        // When/Then
        assertThrows(IllegalStateException.class, () ->
            deviceTokenService.updateTokenPushEnabled(USER_ID, DEVICE_TOKEN, true)
        );
    }

    @Test
    void updateTokenPushEnabled_WhenTokenNotFound_ShouldThrowException() {
        // Given
        when(deviceTokenRepository.findById(any(DeviceTokenId.class))).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            deviceTokenService.updateTokenPushEnabled(USER_ID, DEVICE_TOKEN, true)
        );
    }

    @Test
    void deleteToken_ShouldCallRepository() {
        // When
        deviceTokenService.deleteToken(USER_ID, DEVICE_TOKEN);

        // Then
        verify(deviceTokenRepository).deleteById(new DeviceTokenId(USER_ID, DEVICE_TOKEN));
    }

    @Test
    void registerOrUpdateDeviceToken_WithNullOptionalFields_ShouldNotUpdateExistingValues() {
        // Given
        when(deviceTokenRepository.findById(any(DeviceTokenId.class))).thenReturn(Optional.of(deviceTokenEntity));
        when(deviceTokenRepository.save(any(DeviceTokenEntity.class))).thenReturn(deviceTokenEntity);
        when(deviceTokenMapper.toDto(any(DeviceTokenEntity.class))).thenReturn(deviceTokenResponse);

        String originalOs = deviceTokenEntity.getDeviceOs();
        String originalName = deviceTokenEntity.getDeviceName();

        // When
        DeviceTokenResponse response = deviceTokenService.registerOrUpdateDeviceToken(
                USER_ID, DEVICE_TOKEN, null, null, null);

        // Then
        assertNotNull(response);
        verify(deviceTokenRepository).save(argThat(entity -> 
            entity.getDeviceOs().equals(originalOs) &&
            entity.getDeviceName().equals(originalName)
        ));
    }
}