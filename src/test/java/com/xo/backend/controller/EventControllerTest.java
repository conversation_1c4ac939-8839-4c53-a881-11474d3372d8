package com.xo.backend.controller;

import com.xo.backend.model.dto.request.event.CreateEventRequestDTO;
import com.xo.backend.model.dto.responses.CreateEventResponseDTO;
import com.xo.backend.service.AdminEventService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EventControllerTest {

    public static final int A_VALID_ID = 1;
    public static final String A_VALID_VENUE_ID = "a_valid_venue_id";
    @Mock
    private AdminEventService adminEventService;
    @InjectMocks
    private AdminEventController adminEventController;

    @Test
    void shouldCreateAValidEvent() {
        givenAnEventRequestDTO();
        andAnEventResponseDTOWithId(A_VALID_ID);

        whenAnEventIsCreatedWith(aValidRequestDTO);

        thenTheBodyIsInTheEventResponseDTO();
    }

    private CreateEventRequestDTO aValidRequestDTO;
    private CreateEventResponseDTO createEventResponseDTO;
    private ResponseEntity<CreateEventResponseDTO> result;

    private void givenAnEventRequestDTO() {
        aValidRequestDTO = CreateEventRequestDTO.builder().build();
    }

    @SuppressWarnings("SameParameterValue") //for readability
    private void andAnEventResponseDTOWithId(int eventId) {
        createEventResponseDTO = CreateEventResponseDTO.builder().eventId(eventId).build();
        when(adminEventService.createEvent(A_VALID_VENUE_ID, aValidRequestDTO)).thenReturn(createEventResponseDTO);
    }

    private void whenAnEventIsCreatedWith(CreateEventRequestDTO aValidRequestDTO1) {
        result = adminEventController.createEvent(A_VALID_VENUE_ID, aValidRequestDTO1);
    }

    private void thenTheBodyIsInTheEventResponseDTO() {
        assertEquals(createEventResponseDTO, result.getBody());
    }
}