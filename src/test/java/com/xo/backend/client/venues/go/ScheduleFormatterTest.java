package com.xo.backend.client.venues.go;

import com.xo.backend.model.dto.responses.venueresponse.Schedule;
import com.xo.backend.model.dto.responses.venueresponse.ScheduleAvailability;
import com.xo.backend.model.dto.responses.venueresponse.SpecialSchedule;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.DayOfWeek;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ScheduleFormatterTest {

    @Test
    void shouldFormatSchedules() {
        // Given
        List<SpecialSchedule> specialSchedules = createSampleSchedules();

        // When
        Map<String, String> result = ScheduleFormatter.formatVenueSchedules(specialSchedules);

        // Then
        assertEquals(7, result.size()); // All days of the week

        // Sample validation for Monday
        String mondaySchedule = result.get("Monday");
        assertEquals("09:00-11:00, 13:00-16:00, 22:00-02:00 (next day)", mondaySchedule);

        // Validation for other days
        for (String day : Arrays.asList("Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday")) {
            assertTrue(result.containsKey(day));
            assertEquals(mondaySchedule, result.get(day));
        }
    }

    @Test
    void shouldHandleEmptySchedules() {
        // Given
        List<SpecialSchedule> emptySchedules = List.of();

        // When
        Map<String, String> result = ScheduleFormatter.formatVenueSchedules(emptySchedules);

        // Then
        assertTrue(result.isEmpty());
    }

    /**
     * Parameterized test for various schedule merging scenarios
     */
    @ParameterizedTest(name = "{0}")
    @MethodSource("provideMergeScenarios")
    void shouldCorrectlyMergeTimeSlots(String scenarioName,
                                       List<ScheduleAvailability> availabilities,
                                       Map<String, String> expectedResult) {
        // Given
        List<SpecialSchedule> specialSchedules = createSpecialScheduleWithAvailabilities(availabilities);

        // When
        Map<String, String> result = ScheduleFormatter.formatVenueSchedules(specialSchedules);

        // Then

        assertEquals(expectedResult.size(), result.size(), "Scenario: " + scenarioName);
        expectedResult.forEach((day, expected) -> {
            assertTrue(result.containsKey(day), "Day " + day + " missing in scenario: " + scenarioName);
            assertEquals(expected, result.get(day), "Incorrect schedule for day " + day + " in scenario: " + scenarioName);
        });
    }

    /**
     * Test for multiple special schedules with different types
     */
    @Test
    void shouldHandleMultipleSpecialSchedules() {
        // Given
        List<SpecialSchedule> specialSchedules = new ArrayList<>();

        // Add opening hours schedule
        specialSchedules.add(SpecialSchedule.builder()
                .id(1)
                .schedule(Schedule.builder()
                        .id(1)
                        .name("Opening Hours")
                        .availabilities(List.of(
                                createAvailability(1, "09:00:00", "17:00:00", List.of("MON", "WED", "FRI"))
                        ))
                        .build())
                .type("OPENING_HOURS")
                .build());

        // Add another schedule with a different type (should be ignored)
        specialSchedules.add(SpecialSchedule.builder()
                .id(2)
                .schedule(Schedule.builder()
                        .id(2)
                        .name("Happy Hours")
                        .availabilities(List.of(
                                createAvailability(2, "16:00:00", "18:00:00", List.of("MON", "TUE", "WED", "THU", "FRI"))
                        ))
                        .build())
                .type("HAPPY_HOURS") // Different type, should be ignored
                .build());

        // When
        Map<String, String> result = ScheduleFormatter.formatVenueSchedules(specialSchedules);

        // Then
        assertEquals(7, result.size()); // All days should be present now
        assertEquals("09:00-17:00", result.get("Monday"));
        assertEquals("Closed", result.get("Tuesday")); // Was not in OPENING_HOURS
        assertEquals("09:00-17:00", result.get("Wednesday"));
        assertEquals("Closed", result.get("Thursday")); // Was not in OPENING_HOURS
        assertEquals("09:00-17:00", result.get("Friday"));
        assertEquals("Closed", result.get("Saturday")); // Was not in OPENING_HOURS
        assertEquals("Closed", result.get("Sunday")); // Was not in OPENING_HOURS
    }

    /**
     * Test for edge case with null or incomplete availability data
     */
    @Test
    void shouldHandleNullOrIncompleteAvailabilities() {
        // Given
        List<SpecialSchedule> specialSchedules = new ArrayList<>();

        // Create schedule with null availabilities
        specialSchedules.add(SpecialSchedule.builder()
                .id(1)
                .schedule(Schedule.builder()
                        .id(1)
                        .name("Null Availabilities")
                        .availabilities(null)
                        .build())
                .type("OPENING_HOURS")
                .build());

        // Create schedule with valid and invalid availabilities
        List<ScheduleAvailability> mixedAvailabilities = new ArrayList<>();

        // Valid availability
        mixedAvailabilities.add(createAvailability(1, "10:00:00", "12:00:00", List.of("MON")));

        // Availability with null days
        mixedAvailabilities.add(ScheduleAvailability.builder()
                .id(2)
                .startTime("13:00:00")
                .endTime("15:00:00")
                .daysOfWeek(null)
                .type("DAYS_OF_WEEK")
                .build());

        // Availability with null times
        mixedAvailabilities.add(ScheduleAvailability.builder()
                .id(3)
                .startTime(null)
                .endTime(null)
                .daysOfWeek(List.of("TUE"))
                .type("DAYS_OF_WEEK")
                .build());

        specialSchedules.add(SpecialSchedule.builder()
                .id(2)
                .schedule(Schedule.builder()
                        .id(2)
                        .name("Mixed Availabilities")
                        .availabilities(mixedAvailabilities)
                        .build())
                .type("OPENING_HOURS")
                .build());

        // When
        Map<String, String> result = ScheduleFormatter.formatVenueSchedules(specialSchedules);

        // Then
        assertEquals(7, result.size()); // All days should be present now
        assertEquals("10:00-12:00", result.get("Monday"));
        assertEquals("Closed", result.get("Tuesday")); // Had invalid availability
        assertEquals("Closed", result.get("Wednesday")); // Not in input
        assertEquals("Closed", result.get("Thursday")); // Not in input
        assertEquals("Closed", result.get("Friday")); // Not in input
        assertEquals("Closed", result.get("Saturday")); // Not in input
        assertEquals("Closed", result.get("Sunday")); // Not in input
    }

    /**
     * Test to verify that all days are present in the result
     */
    @Test
    void shouldAlwaysIncludeAllDaysOfWeek() {
        // Given
        List<SpecialSchedule> specialSchedules = new ArrayList<>();
        specialSchedules.add(SpecialSchedule.builder()
                .id(1)
                .schedule(Schedule.builder()
                        .id(1)
                        .name("Limited Days")
                        .availabilities(List.of(
                                createAvailability(1, "09:00:00", "17:00:00", List.of("MON", "WED"))
                        ))
                        .build())
                .type("OPENING_HOURS")
                .build());

        // When
        Map<String, String> result = ScheduleFormatter.formatVenueSchedules(specialSchedules);

        // Then
        assertEquals(7, result.size());
        // Verify days with schedules
        assertEquals("09:00-17:00", result.get("Monday"));
        assertEquals("09:00-17:00", result.get("Wednesday"));

        // Verify days marked as closed
        assertEquals("Closed", result.get("Tuesday"));
        assertEquals("Closed", result.get("Thursday"));
        assertEquals("Closed", result.get("Friday"));
        assertEquals("Closed", result.get("Saturday"));
        assertEquals("Closed", result.get("Sunday"));
    }

    /**
     * Test to verify the order of days in the result
     */
    @Test
    void shouldReturnDaysInCorrectOrder() {
        // Given
        List<SpecialSchedule> specialSchedules = new ArrayList<>();
        specialSchedules.add(SpecialSchedule.builder()
                .id(1)
                .schedule(Schedule.builder()
                        .id(1)
                        .name("Random Order Days")
                        .availabilities(List.of(
                                createAvailability(1, "09:00:00", "12:00:00", List.of("WED")),
                                createAvailability(2, "14:00:00", "18:00:00", List.of("SUN")),
                                createAvailability(3, "10:00:00", "14:00:00", List.of("FRI"))
                        ))
                        .build())
                .type("OPENING_HOURS")
                .build());

        // When
        Map<String, String> result = ScheduleFormatter.formatVenueSchedules(specialSchedules);

        // Then
        List<String> expectedOrder = Arrays.stream(DayOfWeek.values())
                .sorted(Comparator.comparingInt(DayOfWeek::getValue))
                .map(day -> day.getDisplayName(TextStyle.FULL, Locale.ENGLISH))
                .toList();

        // Verify the iteration order matches expected order
        List<String> actualOrder = new ArrayList<>(result.keySet());
        assertEquals(expectedOrder, actualOrder, "Days are not in the expected order");
    }

    /**
     * Parameterized test to verify overnight schedule merging based on end time
     */
    @ParameterizedTest(name = "End time {0} should {1} be merged")
    @MethodSource("provideOvernightEndTimes")
    void shouldCorrectlyHandleOvernightSchedulesBasedOnEndTime(String endTime, String shouldMerge) {
        // Given
        List<SpecialSchedule> specialSchedules = new ArrayList<>();

        // Create a schedule with late night slot ending at 23:59 and early morning slot with the provided end time
        specialSchedules.add(SpecialSchedule.builder()
                .id(1)
                .schedule(Schedule.builder()
                        .id(1)
                        .name("Overnight Schedule")
                        .availabilities(List.of(
                                createAvailability(1, "22:00:00", "23:59:00", List.of("MON")),
                                createAvailability(2, "00:00:00", endTime, List.of("TUE"))
                        ))
                        .build())
                .type("OPENING_HOURS")
                .build());

        // When
        Map<String, String> result = ScheduleFormatter.formatVenueSchedules(specialSchedules);

        // Then
        if (shouldMerge.isEmpty()) {
            // If it should merge, Monday should have the overnight format and Tuesday should be closed
            assertTrue(result.get("Monday").contains(" (next day)"),
                    "Expected Monday to show overnight format for end time " + endTime);
            assertEquals("Closed", result.get("Tuesday"),
                    "Expected Tuesday to be closed after merging for end time " + endTime);
        } else {
            // If it should not merge, Monday and Tuesday should have separate times
            assertFalse(result.get("Monday").contains(" (next day)"),
                    "Monday should not show overnight format for end time " + endTime);
            assertTrue(result.get("Tuesday").startsWith("00:00-" + endTime.substring(0, 5)),
                    "Tuesday should show the regular morning slot for end time " + endTime);
        }
    }

    /**
     * Data provider for overnight schedule end times
     */
    static Stream<Arguments> provideOvernightEndTimes() {
        return Stream.of(
                // End times before 6am - should merge
                Arguments.of("01:00:00", ""),
                Arguments.of("02:30:00", ""),
                Arguments.of("05:59:59", ""),

                // End times at or after 6am - should not merge
                Arguments.of("06:00:00", "not"),
                Arguments.of("06:01:00", "not"),
                Arguments.of("08:30:00", "not"),
                Arguments.of("12:00:00", "not")
        );
    }

    /**
     * Data provider for various time slot merging scenarios
     */
    static Stream<Arguments> provideMergeScenarios() {
        return Stream.of(
                // Scenario 1: Adjacent time slots
                Arguments.of(
                        "Adjacent time slots",
                        List.of(
                                createAvailability(1, "09:00:00", "11:00:00", List.of("MON")),
                                createAvailability(2, "11:00:00", "13:00:00", List.of("MON")),
                                createAvailability(3, "13:00:00", "15:00:00", List.of("MON"))
                        ),
                        Map.of(
                                "Monday", "09:00-15:00",
                                "Tuesday", "Closed",
                                "Wednesday", "Closed",
                                "Thursday", "Closed",
                                "Friday", "Closed",
                                "Saturday", "Closed",
                                "Sunday", "Closed"
                        )
                ),

                // Scenario 2: Overlapping time slots
                Arguments.of(
                        "Overlapping time slots",
                        List.of(
                                createAvailability(1, "09:00:00", "12:00:00", List.of("TUE")),
                                createAvailability(2, "11:00:00", "14:00:00", List.of("TUE")),
                                createAvailability(3, "16:00:00", "18:00:00", List.of("TUE")) // Separate slot
                        ),
                        Map.of(
                                "Monday", "Closed",
                                "Tuesday", "09:00-14:00, 16:00-18:00",
                                "Wednesday", "Closed",
                                "Thursday", "Closed",
                                "Friday", "Closed",
                                "Saturday", "Closed",
                                "Sunday", "Closed"
                        )
                ),

                // Scenario 3: Contained time slots
                Arguments.of(
                        "Contained time slots",
                        List.of(
                                createAvailability(1, "09:00:00", "17:00:00", List.of("WED")),
                                createAvailability(2, "12:00:00", "14:00:00", List.of("WED")) // Contained within first
                        ),
                        Map.of(
                                "Monday", "Closed",
                                "Tuesday", "Closed",
                                "Wednesday", "09:00-17:00",
                                "Thursday", "Closed",
                                "Friday", "Closed",
                                "Saturday", "Closed",
                                "Sunday", "Closed"
                        )
                ),

                // Scenario 4: Multiple overnight slots ending before 6am
                Arguments.of(
                        "Multiple overnight slots ending before 6am",
                        List.of(
                                createAvailability(1, "22:00:00", "23:59:00", List.of("SAT")),
                                createAvailability(2, "00:00:00", "02:00:00", List.of("SUN")),
                                createAvailability(4, "00:00:00", "03:00:00", List.of("MON")), // Another early morning
                                createAvailability(3, "23:00:00", "23:59:00", List.of("SUN")), // Another late night
                                createAvailability(4, "10:00:00", "13:00:00", List.of("MON"))  // Another slot on Monday
                        ),
                        Map.of(
                                "Monday", "10:00-13:00",
                                "Tuesday", "Closed",
                                "Wednesday", "Closed",
                                "Thursday", "Closed",
                                "Friday", "Closed",
                                "Saturday", "22:00-02:00 (next day)",
                                "Sunday", "23:00-03:00 (next day)"
                        )
                ),

                // Scenario 5: Overnight slots ending after 6am - should not merge
                Arguments.of(
                        "Overnight slots ending after 6am",
                        List.of(
                                createAvailability(1, "22:00:00", "23:59:00", List.of("THU")),
                                createAvailability(2, "00:00:00", "08:00:00", List.of("FRI")),
                                createAvailability(3, "10:00:00", "14:00:00", List.of("FRI"))
                        ),
                        Map.of(
                                "Monday", "Closed",
                                "Tuesday", "Closed",
                                "Wednesday", "Closed",
                                "Thursday", "22:00-23:59",
                                "Friday", "00:00-08:00, 10:00-14:00",
                                "Saturday", "Closed",
                                "Sunday", "Closed"
                        )
                ),

                // Scenario 6: Complex merging with gaps and cutoff time consideration
                Arguments.of(
                        "Complex merging with gaps and 6am cutoff",
                        List.of(
                                // Day 1 with overnight that should merge (ends at 5am)
                                createAvailability(1, "22:00:00", "23:59:00", List.of("MON")),
                                createAvailability(2, "00:00:00", "05:00:00", List.of("TUE")),

                                // Day 2 with overnight that should not merge (ends at 7am)
                                createAvailability(3, "22:00:00", "23:59:00", List.of("WED")),
                                createAvailability(4, "00:00:00", "07:00:00", List.of("THU")),

                                // Other slots
                                createAvailability(5, "10:00:00", "14:00:00", List.of("MON", "WED"))
                        ),
                        Map.of(
                                "Monday", "10:00-14:00, 22:00-05:00 (next day)",
                                "Tuesday", "Closed",
                                "Wednesday", "10:00-14:00, 22:00-23:59",
                                "Thursday", "00:00-07:00",
                                "Friday", "Closed",
                                "Saturday", "Closed",
                                "Sunday", "Closed"
                        )
                ),

                //Scenario 7: Small Merging Gaps
                Arguments.of(
                        "Small merging gaps",
                        List.of(
                                createAvailability(1, "09:00:00", "10:00:00", List.of("MON")),
                                createAvailability(2, "10:01:00", "11:00:00", List.of("MON")),
                                createAvailability(3, "11:01:00", "12:00:00", List.of("MON")),
                                createAvailability(4, "12:02:00", "13:00:00", List.of("MON")) // Should not merge
                        ),
                        Map.of(
                                "Monday", "09:00-12:00, 12:02-13:00",
                                "Tuesday", "Closed",
                                "Wednesday", "Closed",
                                "Thursday", "Closed",
                                "Friday", "Closed",
                                "Saturday", "Closed",
                                "Sunday", "Closed"
                        )
                ),

                //Scenario 8: Small Merging Gaps
                Arguments.of(
                        "Closed on weekends",
                        List.of(
                                createAvailability(1, "22:00:00", "23:59:00", List.of("MON","TUE","WED","THU","FRI")),
                                createAvailability(2, "00:00:00", "05:00:00", List.of("MON","TUE","WED","THU","FRI"))
                        ),
                        Map.of(
                                "Monday", "00:00-05:00, 22:00-05:00 (next day)",
                                "Tuesday", "22:00-05:00 (next day)",
                                "Wednesday", "22:00-05:00 (next day)",
                                "Thursday", "22:00-05:00 (next day)",
                                "Friday", "22:00-23:59",
                                "Saturday", "Closed",
                                "Sunday", "Closed"
                        )
                )
        );
    }

    /**
     * Helper method to create a schedule availability
     */
    private static ScheduleAvailability createAvailability(int id, String startTime, String endTime, List<String> days) {
        return ScheduleAvailability.builder()
                .id(id)
                .startTime(startTime)
                .endTime(endTime)
                .daysOfWeek(days)
                .type("DAYS_OF_WEEK")
                .build();
    }

    /**
     * Helper method to create special schedule with given availabilities
     */
    private static List<SpecialSchedule> createSpecialScheduleWithAvailabilities(List<ScheduleAvailability> availabilities) {
        Schedule schedule = Schedule.builder()
                .id(1)
                .name("Test Schedule")
                .availabilities(availabilities)
                .build();

        SpecialSchedule specialSchedule = SpecialSchedule.builder()
                .id(1)
                .schedule(schedule)
                .type("OPENING_HOURS")
                .build();

        return List.of(specialSchedule);
    }

    private List<SpecialSchedule> createSampleSchedules() {
        // Morning availability
        ScheduleAvailability morning = ScheduleAvailability.builder()
                .id(27534)
                .startTime("09:00:00")
                .endTime("11:00:00")
                .daysOfWeek(List.of("MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"))
                .type("DAYS_OF_WEEK")
                .build();

        // Afternoon availability
        ScheduleAvailability afternoon = ScheduleAvailability.builder()
                .id(27535)
                .startTime("13:00:00")
                .endTime("16:00:00")
                .daysOfWeek(List.of("MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"))
                .type("DAYS_OF_WEEK")
                .build();

        // Late night availability
        ScheduleAvailability lateNight = ScheduleAvailability.builder()
                .id(27536)
                .startTime("22:00:00")
                .endTime("23:59:00")
                .daysOfWeek(List.of("MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"))
                .type("DAYS_OF_WEEK")
                .build();

        // Early morning availability
        ScheduleAvailability earlyMorning = ScheduleAvailability.builder()
                .id(27537)
                .startTime("00:00:00")
                .endTime("02:00:00")
                .daysOfWeek(List.of("MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"))
                .type("DAYS_OF_WEEK")
                .build();

        // Create the schedule
        Schedule schedule = Schedule.builder()
                .id(15576)
                .name("General")
                .availabilities(List.of(morning, afternoon, lateNight, earlyMorning))
                .build();

        // Create the special schedule
        SpecialSchedule specialSchedule = SpecialSchedule.builder()
                .id(14147)
                .schedule(schedule)
                .type("OPENING_HOURS")
                .build();

        return List.of(specialSchedule);
    }
}