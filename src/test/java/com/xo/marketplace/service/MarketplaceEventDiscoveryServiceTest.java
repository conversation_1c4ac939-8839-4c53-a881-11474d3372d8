package com.xo.marketplace.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.mappers.EventDetailsMapper;
import com.xo.backend.model.dto.EventBrowseDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MarketplaceEventDiscoveryServiceTest {

    @Mock
    private EventRepository eventRepository;

    @Mock
    private VenueInfoService venueInfoService;

    @Mock
    private EventDetailsMapper eventDetailsMapper;

    @InjectMocks
    private MarketplaceEventDiscoveryService service;

    @Test
    void getMarketplaceEvents_returnsEmptyPage_whenNoEventsFound() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        when(eventRepository.findMarketplaceEvents(pageable)).thenReturn(Page.empty(pageable));

        // Act
        Page<EventBrowseDTO> result = service.getMarketplaceEvents(null,pageable);

        // Assert
        assertTrue(result.isEmpty());
        verify(eventRepository).findMarketplaceEvents(pageable);
        verifyNoInteractions(venueInfoService);
        verifyNoInteractions(eventDetailsMapper);
    }

    @Test
    void getMarketplaceEvents_returnsEvents_withValidVenueInfo() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Create event with valid venue ID
        EventEntity event = new EventEntity();
        event.setId(1);
        event.setVenueId("123");
        List<EventEntity> events = List.of(event);
        Page<EventEntity> eventPage = new PageImpl<>(events, pageable, 1);

        // Create venue response
        VenueOverviewDTO venue = VenueOverviewDTO.builder()
                .storeId(123L)
                .name("Test Venue")
                .build();
        Map<Long, VenueOverviewDTO> venueMap = Map.of(123L, venue);

        // Create expected output
        EventBrowseDTO expectedDto = EventBrowseDTO.builder().build();

        // Configure mocks
        when(eventRepository.findMarketplaceEvents(pageable)).thenReturn(eventPage);
        when(venueInfoService.getVenuesOverview(anySet())).thenReturn(venueMap);
        when(eventDetailsMapper.mapToEventsBrowserDTO(event, venue)).thenReturn(expectedDto);

        // Act
        Page<EventBrowseDTO> result = service.getMarketplaceEvents(null,pageable);

        // Assert
        assertEquals(1, result.getTotalElements());
        assertEquals(expectedDto, result.getContent().getFirst());

        verify(eventRepository).findMarketplaceEvents(pageable);
        verify(venueInfoService).getVenuesOverview(anySet());
        verify(eventDetailsMapper).mapToEventsBrowserDTO(event, venue);
    }

    @Test
    void getMarketplaceEvents_handlesEvents_withInvalidVenueId() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Create event with invalid venue ID
        EventEntity eventWithInvalidVenue = new EventEntity();
        eventWithInvalidVenue.setId(1);
        eventWithInvalidVenue.setVenueId("invalid");

        List<EventEntity> events = List.of(eventWithInvalidVenue);
        Page<EventEntity> eventPage = new PageImpl<>(events, pageable, 1);

        // Create expected output
        EventBrowseDTO expectedDto = EventBrowseDTO.builder().build();

        // Configure mocks
        when(eventRepository.findMarketplaceEvents(pageable)).thenReturn(eventPage);
        when(venueInfoService.getVenuesOverview(Collections.emptySet())).thenReturn(Collections.emptyMap());
        when(eventDetailsMapper.mapToEventsBrowserDTO(eq(eventWithInvalidVenue), isNull())).thenReturn(expectedDto);

        // Act
        Page<EventBrowseDTO> result = service.getMarketplaceEvents(null,pageable);

        // Assert
        assertEquals(1, result.getTotalElements());
        assertEquals(expectedDto, result.getContent().getFirst());

        verify(eventRepository).findMarketplaceEvents(pageable);
        verify(venueInfoService).getVenuesOverview(Collections.emptySet());
        verify(eventDetailsMapper).mapToEventsBrowserDTO(eq(eventWithInvalidVenue), isNull());
    }

    @Test
    void getMarketplaceEvents_handlesEvents_withMixedValidAndInvalidVenueIds() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Create events with mixed venue IDs
        EventEntity validEvent = new EventEntity();
        validEvent.setId(1);
        validEvent.setVenueId("123");

        EventEntity invalidEvent = new EventEntity();
        invalidEvent.setId(2);
        invalidEvent.setVenueId("invalid");

        List<EventEntity> events = List.of(validEvent, invalidEvent);
        Page<EventEntity> eventPage = new PageImpl<>(events, pageable, 2);

        // Create venue response
        VenueOverviewDTO venue = VenueOverviewDTO.builder()
                .storeId(123L)
                .name("Test Venue")
                .build();
        Map<Long, VenueOverviewDTO> venueMap = Map.of(123L, venue);

        // Create expected outputs
        EventBrowseDTO expectedDto1 = EventBrowseDTO.builder().build();
        EventBrowseDTO expectedDto2 = EventBrowseDTO.builder().build();

        // Configure mocks
        when(eventRepository.findMarketplaceEvents(pageable)).thenReturn(eventPage);
        when(venueInfoService.getVenuesOverview(anySet())).thenReturn(venueMap);
        when(eventDetailsMapper.mapToEventsBrowserDTO(validEvent, venue)).thenReturn(expectedDto1);
        when(eventDetailsMapper.mapToEventsBrowserDTO(invalidEvent, null)).thenReturn(expectedDto2);

        // Act
        Page<EventBrowseDTO> result = service.getMarketplaceEvents(null,pageable);

        // Assert
        assertEquals(2, result.getTotalElements());

        verify(eventRepository).findMarketplaceEvents(pageable);
        verify(venueInfoService).getVenuesOverview(anySet());
        verify(eventDetailsMapper).mapToEventsBrowserDTO(validEvent, venue);
        verify(eventDetailsMapper).mapToEventsBrowserDTO(invalidEvent, null);
    }
}