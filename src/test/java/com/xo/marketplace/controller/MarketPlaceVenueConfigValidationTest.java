package com.xo.marketplace.controller;

import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class MarketPlaceVenueConfigValidationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    @DisplayName("Should return validation error when venueId is null")
    void validateMarketPlaceVenueConfigDTO_WhenVenueIdIsNull_ShouldReturnValidationError() {
        // Arrange
        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId(null) // Required field is null
                .visible(true)
                .feeConfig(null)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert and Debug
        System.out.println("Number of violations: " + violations.size());
        violations.forEach(violation -> {
            System.out.println("Property path: " + violation.getPropertyPath());
            System.out.println("Message: " + violation.getMessage());
            System.out.println("Invalid value: " + violation.getInvalidValue());
            System.out.println("---");
        });

        assertThat(violations).isNotEmpty();
        assertThat(violations).anyMatch(v -> v.getPropertyPath().toString().equals("venueId"));
    }

    @Test
    @DisplayName("Should return validation error when visible is null")
    void validateMarketPlaceVenueConfigDTO_WhenVisibleIsNull_ShouldReturnValidationError() {
        // Arrange
        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(null) // Required field is null
                .feeConfig(null)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert and Debug
        System.out.println("Number of violations: " + violations.size());
        violations.forEach(violation -> {
            System.out.println("Property path: " + violation.getPropertyPath());
            System.out.println("Message: " + violation.getMessage());
            System.out.println("Invalid value: " + violation.getInvalidValue());
            System.out.println("---");
        });

        assertThat(violations).isNotEmpty();
        assertThat(violations).anyMatch(v -> v.getPropertyPath().toString().equals("visible"));
    }

    @Test
    @DisplayName("Should return validation errors when both venueId and visible are null")
    void validateMarketPlaceVenueConfigDTO_WhenBothVenueIdAndVisibleAreNull_ShouldReturnValidationErrors() {
        // Arrange
        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId(null) // Required field is null
                .visible(null) // Required field is null
                .feeConfig(null)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert and Debug
        System.out.println("Number of violations: " + violations.size());
        violations.forEach(violation -> {
            System.out.println("Property path: " + violation.getPropertyPath());
            System.out.println("Message: " + violation.getMessage());
            System.out.println("Invalid value: " + violation.getInvalidValue());
            System.out.println("---");
        });

        assertThat(violations).hasSize(2);
        assertThat(violations).anyMatch(v -> v.getPropertyPath().toString().equals("venueId"));
        assertThat(violations).anyMatch(v -> v.getPropertyPath().toString().equals("visible"));
    }

    @Test
    @DisplayName("Should succeed when all required fields are provided")
    void validateMarketPlaceVenueConfigDTO_WhenAllRequiredFieldsAreProvided_ShouldSucceed() {
        // Arrange
        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(null) // Optional
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert
        assertThat(violations).isEmpty();
    }

    @Test
    @DisplayName("Should return validation errors for both top-level and nested fields")
    void validateMarketPlaceVenueConfigDTO_WhenBothTopLevelAndNestedFieldsAreInvalid_ShouldReturnAllValidationErrors() {
        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(null) // Required field is null
                .appFeeFixedPerTransaction(new BigDecimal("2.00"))
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId(null) // Required field is null
                .visible(null) // Required field is null
                .feeConfig(invalidFeeConfig)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert and Debug
        System.out.println("Number of violations: " + violations.size());
        violations.forEach(violation -> {
            System.out.println("Property path: " + violation.getPropertyPath());
            System.out.println("Message: " + violation.getMessage());
            System.out.println("Invalid value: " + violation.getInvalidValue());
            System.out.println("---");
        });

        assertThat(violations).hasSize(3); // venueId, visible, and feeConfig.appFeePerc
        assertThat(violations).anyMatch(v -> v.getPropertyPath().toString().equals("venueId"));
        assertThat(violations).anyMatch(v -> v.getPropertyPath().toString().equals("visible"));
        assertThat(violations).anyMatch(v -> v.getPropertyPath().toString().equals("feeConfig.appFeePerc"));
    }
}
