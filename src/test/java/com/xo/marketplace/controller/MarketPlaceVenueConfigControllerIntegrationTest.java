package com.xo.marketplace.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import com.xo.marketplace.service.MarketPlaceVenueConfigService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.List;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(MarketPlaceVenueConfigController.class)
class MarketPlaceVenueConfigControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private MarketPlaceVenueConfigService marketPlaceVenueConfigService;

    @Test
    @DisplayName("Should return 400 when feeConfig has null required fields")
    @WithMockUser(roles = "ADMIN")
    void createOrUpdateBatchMarketPlaceVenueConfig_WhenFeeConfigHasNullRequiredFields_ShouldReturn400() throws Exception {
        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(null) // Required field is null
                .appFeeFixedPerTransaction(new BigDecimal("2.00"))
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(invalidFeeConfig)
                .build();

        List<MarketPlaceVenueConfigDTO> configDTOs = List.of(configDTO);

        // Act & Assert
        mockMvc.perform(post("/v1/marketplace/venues/config/batch")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(configDTOs)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("appFeePerc is required when feeConfig is provided")));
    }

    @Test
    @DisplayName("Should return 400 when multiple required fields are null")
    @WithMockUser(roles = "ADMIN")
    void createOrUpdateBatchMarketPlaceVenueConfig_WhenMultipleRequiredFieldsAreNull_ShouldReturn400() throws Exception {
        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(null) // Required field is null
                .appFeeFixedPerTransaction(null) // Required field is null
                .appFeeFixedPerEntryPass(null) // Required field is null
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(null) // Required field is null
                .pspFeePerc(null) // Required field is null
                .pspFeeFixed(null) // Required field is null
                .passPspFees(null) // Required field is null
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(invalidFeeConfig)
                .build();

        List<MarketPlaceVenueConfigDTO> configDTOs = List.of(configDTO);

        // Act & Assert
        mockMvc.perform(post("/v1/marketplace/venues/config/batch")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(configDTOs)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").exists());
    }
}
