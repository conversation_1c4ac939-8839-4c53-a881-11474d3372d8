package com.xo.marketplace.controller;

import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class ConstraintViolationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    @DisplayName("Should demonstrate ConstraintViolationException vs MethodArgumentNotValidException")
    void testConstraintViolationExceptionForList() {
        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(null) // Required field is null
                .appFeeFixedPerTransaction(null) // Required field is null
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(invalidFeeConfig)
                .build();

        List<MarketPlaceVenueConfigDTO> configDTOs = List.of(configDTO);

        // Act - Validate the list (this simulates what happens in the controller)
        Set<ConstraintViolation<List<MarketPlaceVenueConfigDTO>>> violations = validator.validate(configDTOs);

        // This won't work because we're validating the list itself, not its contents
        // In the controller, Spring validates each element due to @Valid annotations
        
        // Let's validate each element individually to see the violations
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> elementViolations = validator.validate(configDTO);

        // Assert and Debug
        System.out.println("List violations: " + violations.size());
        System.out.println("Element violations: " + elementViolations.size());
        
        elementViolations.forEach(violation -> {
            System.out.println("Property path: " + violation.getPropertyPath());
            System.out.println("Message: " + violation.getMessage());
            System.out.println("---");
        });

        assertThat(elementViolations).isNotEmpty();
        assertThat(elementViolations).hasSize(2);
    }

    @Test
    @DisplayName("Should simulate ConstraintViolationException message formatting")
    void testConstraintViolationMessageFormatting() {
        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(null) // Required field is null
                .appFeeFixedPerTransaction(null) // Required field is null
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(invalidFeeConfig)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Simulate what ConstraintViolationException would contain
        StringBuilder message = new StringBuilder();
        for (ConstraintViolation<MarketPlaceVenueConfigDTO> violation : violations) {
            String propertyPath = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();
            message.append(propertyPath).append(": ").append(errorMessage).append("; ");
        }
        
        if (!message.isEmpty()) {
            message.setLength(message.length() - 2); // Remove trailing "; "
        }

        System.out.println("Formatted message: " + message.toString());

        // Assert
        assertThat(message.toString()).contains("feeConfig.appFeePerc: appFeePerc is required when feeConfig is provided");
        assertThat(message.toString()).contains("feeConfig.appFeeFixedPerTransaction: appFeeFixedPerTransaction is required when feeConfig is provided");
    }
}
