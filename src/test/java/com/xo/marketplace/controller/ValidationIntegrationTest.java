package com.xo.marketplace.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.util.List;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureWebMvc
class ValidationIntegrationTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("Should return 400 with detailed validation errors when feeConfig has null required fields")
    @WithMockUser(roles = "ADMIN")
    void testValidationErrorResponse() throws Exception {
        MockMvc mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();

        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(null) // Required field is null
                .appFeeFixedPerTransaction(null) // Required field is null
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(invalidFeeConfig)
                .build();

        List<MarketPlaceVenueConfigDTO> configDTOs = List.of(configDTO);

        // Act & Assert
        mockMvc.perform(post("/v1/marketplace/venues/config/batch")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(configDTOs)))
                .andDo(print())
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.errorCode").value("REQUEST_PARAMS_VALIDATION_ERROR"));
    }
}
