package com.xo.marketplace.controller;

import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class ValidationErrorTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    @DisplayName("Should show detailed validation error messages")
    void testValidationErrorMessages() {
        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(null) // Required field is null
                .appFeeFixedPerTransaction(null) // Required field is null
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(invalidFeeConfig)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert and Debug
        System.out.println("Number of violations: " + violations.size());
        violations.forEach(violation -> {
            System.out.println("Property path: " + violation.getPropertyPath());
            System.out.println("Message: " + violation.getMessage());
            System.out.println("Invalid value: " + violation.getInvalidValue());
            System.out.println("Root bean: " + violation.getRootBean().getClass().getSimpleName());
            System.out.println("---");
        });

        assertThat(violations).isNotEmpty();
    }
}
