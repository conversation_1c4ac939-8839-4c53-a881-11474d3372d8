version: '3.8'

services:
  db:
    image: postgres:14.6-alpine
    container_name: xo_db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=mysecretpassword
      - POSTGRES_DB=postgres
    volumes:
      - postgresvol:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - local-network
    healthcheck:
      test: [ "CMD", "pg_isready", "-q", "-U", "postgres" ]
      interval: 5s
      timeout: 1s
      retries: 2
  s3mock:
    image: adobe/s3mock:latest
    environment:
      - debug=true
      - retainFilesOnExit=true
      - root=containers3root
      - initialBuckets=mybucket
    ports:
      - "9090:9090"
      - "9191:9191"
    volumes:
      - ./locals3root:/containers3root

networks:
  local-network:
    driver: bridge
volumes:
  postgresvol:

