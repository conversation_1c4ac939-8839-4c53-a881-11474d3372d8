name: PR Build Verification

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  gradle-check:
    name: Gradle Build Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '21'
          cache: 'gradle'

      - name: Verify project builds
        run: |
          echo "Starting Gradle build verification..."
          sudo apt-get update
          chmod +x ./gradlew
          ./gradlew --stacktrace --no-daemon clean build
          echo "Gradle build completed successfully"

  docker-check:
    name: Docker Build Check
    runs-on: ubuntu-latest
    needs: gradle-check
    steps:
      - uses: actions/checkout@v3
      - name: Verify Docker build (no push)
        env:
          SPRING_PROFILE: develop
          PORT: 8080
          GO_API_KEY: ${{ secrets.GO_API_KEY }}
          CONTABO_ACCESS_KEY: ${{ secrets.CONTABO_ACCESS_KEY }}
          CONTABO_SECRET_KEY: ${{ secrets.CONTABO_SECRET_KEY }}
          STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
          STRIPE_CONNECT_ENDPOINT_SECRET: ${{ secrets.STRIPE_CONNECT_ENDPOINT_SECRET }}
          OVH_ACCESS_KEY: ${{ secrets.OVH_ACCESS_KEY }}
          OVH_SECRET_KEY: ${{ secrets.OVH_SECRET_KEY }}
          AWS_SES_USER: ${{ secrets.DEVELOP_AWS_SES_USER }}
          AWS_SES_PASSWORD: ${{ secrets.DEVELOP_AWS_SES_PASSWORD }}
          FIREBASE_CONFIG: ${{ secrets.FIREBASE_CONFIG }}
        run: |
          echo "Starting Docker build verification..."
          
          # Enable BuildKit for better error messages
          export DOCKER_BUILDKIT=1
          
          # Run the build with detailed output but don't push
          docker build --progress=plain --build-arg SPRING_PROFILE=$SPRING_PROFILE \
          --build-arg PORT=$PORT --build-arg GO_API_KEY=$GO_API_KEY \
          --build-arg CONTABO_ACCESS_KEY=$CONTABO_ACCESS_KEY \
          --build-arg CONTABO_SECRET_KEY=$CONTABO_SECRET_KEY \
          --build-arg STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY \
          --build-arg STRIPE_CONNECT_ENDPOINT_SECRET=$STRIPE_CONNECT_ENDPOINT_SECRET \
          --build-arg OVH_ACCESS_KEY=$OVH_ACCESS_KEY \
          --build-arg OVH_SECRET_KEY=$OVH_SECRET_KEY \
          --build-arg AWS_SES_USER=$AWS_SES_USER \
          --build-arg AWS_SES_PASSWORD=$AWS_SES_PASSWORD \
          --build-arg FIREBASE_CONFIG=$FIREBASE_CONFIG \
          -t pr-verification-only .
          
          echo "Docker build completed successfully"