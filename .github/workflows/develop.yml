name: CD Develop

on:
  push:
    branches:
      - develop

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v1
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: develop/xo-events
          IMAGE_TAG: ${{ github.sha }}
          SPRING_PROFILE: develop
          PORT: 8080
          GO_API_KEY: ${{ secrets.GO_API_KEY }}
          CONTABO_ACCESS_KEY: ${{ secrets.CONTABO_ACCESS_KEY }}
          CONTABO_SECRET_KEY: ${{ secrets.CONTABO_SECRET_KEY }}
          STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
          STRIPE_CONNECT_ENDPOINT_SECRET: ${{ secrets.STRIPE_CONNECT_ENDPOINT_SECRET }}
          OVH_ACCESS_KEY: ${{ secrets.OVH_ACCESS_KEY }}
          OVH_SECRET_KEY: ${{ secrets.OVH_SECRET_KEY }}
          AWS_SES_USER: ${{ secrets.DEVELOP_AWS_SES_USER }}
          AWS_SES_PASSWORD: ${{ secrets.DEVELOP_AWS_SES_PASSWORD }}
          FIREBASE_CONFIG: ${{ secrets.FIREBASE_CONFIG }}
          ELASTICSEARCH_USER: ${{ secrets.ELASTICSEARCH_USER }}
          ELASTICSEARCH_PASS: ${{ secrets.ELASTICSEARCH_PASS }}
          ELASTIC_APM_SECRET_TOKEN: ${{ secrets.ELASTICSEARCH_APM_SECRET_TOKEN }}
          DATASOURCE_USERNAME: ${{ secrets.DEVELOP_DATASOURCE_USERNAME }}
          DATASOURCE_PASSWORD: ${{ secrets.DEVELOP_DATASOURCE_PASSWORD }}
        run: |
          docker build --build-arg SPRING_PROFILE=$SPRING_PROFILE \
          --build-arg PORT=$PORT --build-arg GO_API_KEY=$GO_API_KEY \
          --build-arg CONTABO_ACCESS_KEY=$CONTABO_ACCESS_KEY \
          --build-arg CONTABO_SECRET_KEY=$CONTABO_SECRET_KEY \
          --build-arg STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY \
          --build-arg STRIPE_CONNECT_ENDPOINT_SECRET=$STRIPE_CONNECT_ENDPOINT_SECRET \
          --build-arg OVH_ACCESS_KEY=$OVH_ACCESS_KEY \
          --build-arg OVH_SECRET_KEY=$OVH_SECRET_KEY \
          --build-arg AWS_SES_USER=$AWS_SES_USER \
          --build-arg AWS_SES_PASSWORD=$AWS_SES_PASSWORD \
          --build-arg FIREBASE_CONFIG=$FIREBASE_CONFIG \
          --build-arg ELASTICSEARCH_USER=$ELASTICSEARCH_USER \
          --build-arg ELASTICSEARCH_PASS=$ELASTICSEARCH_PASS \
          --build-arg ELASTIC_APM_SECRET_TOKEN=$ELASTIC_APM_SECRET_TOKEN \
          --build-arg DATASOURCE_USERNAME=$DATASOURCE_USERNAME \
          --build-arg DATASOURCE_PASSWORD=$DATASOURCE_PASSWORD \
          -t $ECR_REPOSITORY:$IMAGE_TAG .
          docker tag $ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker tag $ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

      - name: Logout of Amazon ECR
        if: always()
        run: docker logout ${{ steps.login-ecr.outputs.registry }}

      - name: Deploy to develop
        uses: appleboy/ssh-action@v0.0.7
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: develop/xo-events
          IMAGE_TAG: ${{ github.sha }}
        with:
          host: ${{ secrets.DEVELOP_HOST }}
          username: ${{ secrets.DEVELOP_USER }}
          password: ${{ secrets.DEVELOP_PASS }}
          port: ${{ secrets.DEVELOP_SSH_PORT }}
          envs: ECR_REGISTRY,ECR_REPOSITORY,IMAGE_TAG
          script: |
            aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin $ECR_REGISTRY
            docker service update --image $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG xo-infra_xo-events --with-registry-auth
