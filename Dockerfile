FROM openjdk:21-jdk-slim AS build

# Install dependencies
RUN apt-get update && \
    apt-get install -y wget unzip && \
    wget https://services.gradle.org/distributions/gradle-8.8-bin.zip && \
    unzip gradle-8.8-bin.zip -d /opt/ && \
    ln -s /opt/gradle-8.8/bin/gradle /usr/bin/gradle && \
    apt-get clean && rm -rf /var/lib/apt/lists/* gradle-8.8-bin.zip

# Set workdir
WORKDIR /app

# Copy files to path
COPY --chown=gradle:gradle . .

# Build project
RUN gradle build --no-daemon

FROM openjdk:21-jdk-slim

RUN apt-get update && \
    apt-get install -y wget

# Get args 
ARG SPRING_PROFILE=default
ARG PORT=8080
ARG GO_API_KEY=none
ARG CONTABO_ACCESS_KEY=none
ARG CONTABO_SECRET_KEY=none
ARG STRIPE_SECRET_KEY=none
ARG STRIPE_CONNECT_ENDPOINT_SECRET=none
ARG OVH_ACCESS_KEY=none
ARG OVH_SECRET_KEY=none
ARG AWS_SES_USER=none
ARG AWS_SES_PASSWORD=none
ARG FIREBASE_CONFIG=none
ARG ELASTICSEARCH_USER=none
ARG ELASTICSEARCH_PASS=none
ARG ELASTIC_APM_SECRET_TOKEN=none
ARG DATASOURCE_URL=none
ARG DATASOURCE_USERNAME=none
ARG DATASOURCE_PASSWORD=none

# Set environment variables
ENV SPRING_PROFILE=${SPRING_PROFILE}
ENV PORT=${PORT}
ENV GO_API_KEY=${GO_API_KEY}
ENV CONTABO_ACCESS_KEY=${CONTABO_ACCESS_KEY}
ENV CONTABO_SECRET_KEY=${CONTABO_SECRET_KEY}
ENV STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
ENV STRIPE_CONNECT_ENDPOINT_SECRET=${STRIPE_CONNECT_ENDPOINT_SECRET}
ENV OVH_ACCESS_KEY=${OVH_ACCESS_KEY}
ENV OVH_SECRET_KEY=${OVH_SECRET_KEY}
ENV AWS_SES_USER=${AWS_SES_USER}
ENV AWS_SES_PASSWORD=${AWS_SES_PASSWORD}
ENV FIREBASE_CONFIG=${FIREBASE_CONFIG}
ENV ELASTICSEARCH_USER=${ELASTICSEARCH_USER}
ENV ELASTICSEARCH_PASS=${ELASTICSEARCH_PASS}
ENV ELASTIC_APM_SECRET_TOKEN=${ELASTIC_APM_SECRET_TOKEN}
ENV DATASOURCE_URL=${DATASOURCE_URL}
ENV DATASOURCE_USERNAME=${DATASOURCE_USERNAME}
ENV DATASOURCE_PASSWORD=${DATASOURCE_PASSWORD}

# Set workdir
WORKDIR /app

# Copy elastic-apm-agent.jar
COPY --from=docker.elastic.co/observability/apm-agent-java:latest /usr/agent/elastic-apm-agent.jar /elastic-apm-agent.jar

# Copy jar file
COPY --from=build /app/build/libs/*.jar /app/app.jar

# Exxpose port
EXPOSE ${PORT}

# Check if the application is available
HEALTHCHECK CMD ["wget", "-q", "-O", "/dev/null", "http://localhost:8080/actuator/health"]

# Run application
ENTRYPOINT ["java", "-javaagent:/elastic-apm-agent.jar", "-jar", "/app/app.jar", "--spring.profiles.active=default,${SPRING_PROFILE}"]