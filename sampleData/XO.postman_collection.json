{"info": {"_postman_id": "7db0d4c9-06d2-4912-a2f3-3e570636e6db", "name": "XO", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29331986"}, "item": [{"name": "Booking Tier", "item": [{"name": "Create booking tier", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"type\": \"TICKET\",\r\n    \"payOnlinePrice\": \"81.0\",\r\n    \"tierName\": \"first-class\",\r\n    \"description\": \"this tier for first class\",\r\n    \"numberOfDrinks\": 10,\r\n    \"bottleCredit\": 1.0,\r\n    \"deposit\": 50.0,\r\n    \"minimumSpent\": 70.0,\r\n    \"bookingTierSchedules\": [\r\n        {\r\n            \"validFrom\": \"2024-09-08T14:30:00\",\r\n            \"validUntil\": \"2024-09-09T14:30:00\"\r\n        }\r\n    ],\r\n    \"eventId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/booking-tiers", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers"]}}, "response": [{"name": "New Request", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"type\":\"TICKET\",\r\n    \"payOnlinePrice\":\"81.0\",\r\n    \"name\":\"first-class\",\r\n    \"description\":\"this tier for first class\",\r\n    \"numberOfDrinks\":10,\r\n    \"bottleCredit\":1.0,\r\n    \"deposit\":50.0,\r\n    \"minimumSpent\":70.0,\r\n    \"eventId\":1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/booking-tiers", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sat, 07 Sep 2024 18:11:21 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"bookingTierId\": 6\n}"}]}, {"name": "Get booking tier", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/booking-tiers/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers", "6"]}}, "response": [{"name": "Get booking tier", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/booking-tiers/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers", "6"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sun, 08 Sep 2024 16:35:44 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"type\": \"TICKET\",\n    \"payOnlinePrice\": 81,\n    \"tierName\": null,\n    \"description\": \"this tier for first class\",\n    \"numberOfDrinks\": 10,\n    \"bottleCredit\": 1,\n    \"minimumSpent\": 70,\n    \"bookingTierSchedules\": [\n        {\n            \"id\": 11,\n            \"validFrom\": \"2024-09-08T14:30:00.000+00:00\",\n            \"validUntil\": \"2024-09-09T14:30:00.000+00:00\"\n        }\n    ],\n    \"availability\": 0,\n    \"eventId\": 1\n}"}]}, {"name": "Get booking tier by event id", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/booking-tiers/event/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers", "event", "1"]}}, "response": [{"name": "Get booking tier by event id", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/booking-tiers/event/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers", "event", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sun, 08 Sep 2024 16:36:43 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "[\n    {\n        \"type\": \"TICKET\",\n        \"payOnlinePrice\": 99.99,\n        \"tierName\": \"VIP\",\n        \"description\": \"description\",\n        \"numberOfDrinks\": 3,\n        \"bottleCredit\": 30,\n        \"minimumSpent\": 0,\n        \"bookingTierSchedules\": [\n            {\n                \"id\": 1,\n                \"validFrom\": \"2024-08-01T07:10:00.000+00:00\",\n                \"validUntil\": \"2024-08-10T08:10:00.000+00:00\"\n            },\n            {\n                \"id\": 2,\n                \"validFrom\": \"2024-08-20T07:10:00.000+00:00\",\n                \"validUntil\": \"2024-08-30T08:10:00.000+00:00\"\n            }\n        ],\n        \"availability\": 0,\n        \"eventId\": 1\n    },\n    {\n        \"type\": \"TICKET\",\n        \"payOnlinePrice\": 81,\n        \"tierName\": null,\n        \"description\": \"this tier for first class\",\n        \"numberOfDrinks\": 10,\n        \"bottleCredit\": 1,\n        \"minimumSpent\": 70,\n        \"bookingTierSchedules\": [\n            {\n                \"id\": 11,\n                \"validFrom\": \"2024-09-08T14:30:00.000+00:00\",\n                \"validUntil\": \"2024-09-09T14:30:00.000+00:00\"\n            }\n        ],\n        \"availability\": 0,\n        \"eventId\": 1\n    }\n]"}]}, {"name": "Update Booking Tier", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"type\": \"TICKET\",\r\n    \"payOnlinePrice\": \"81.0\",\r\n    \"tierName\": \"first-class\",\r\n    \"description\": \"this tier for first class\",\r\n    \"numberOfDrinks\": 10,\r\n    \"bottleCredit\": 1.0,\r\n    \"deposit\": 50.0,\r\n    \"minimumSpent\": 70.0,\r\n    \"bookingTierSchedules\": [\r\n        {\r\n            \"validFrom\": \"2024-09-08T14:30:00\",\r\n            \"validUntil\": \"2024-09-09T14:30:00\"\r\n        }\r\n    ],\r\n    \"eventId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/booking-tiers/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers", "6"]}}, "response": [{"name": "Update Booking Tier", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"type\": \"TICKET\",\r\n    \"payOnlinePrice\": \"81.0\",\r\n    \"tierName\": \"first-class\",\r\n    \"description\": \"this tier for first class\",\r\n    \"numberOfDrinks\": 10,\r\n    \"bottleCredit\": 1.0,\r\n    \"deposit\": 50.0,\r\n    \"minimumSpent\": 70.0,\r\n    \"bookingTierSchedules\": [\r\n        {\r\n            \"validFrom\": \"2024-09-08T14:30:00\",\r\n            \"validUntil\": \"2024-09-09T14:30:00\"\r\n        }\r\n    ],\r\n    \"eventId\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/booking-tiers/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers", "6"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sun, 08 Sep 2024 16:49:41 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"bookingTierId\": 6\n}"}]}, {"name": "Delete booking tier", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:8080/v1/booking-tiers/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers", "6"]}}, "response": [{"name": "New Request", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:8080/v1/booking-tiers/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "booking-tiers", "6"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "text/plain;charset=UTF-8"}, {"key": "Content-Length", "value": "33"}, {"key": "Date", "value": "Sun, 08 Sep 2024 16:50:08 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "Booking Tier deleted successfully"}]}]}, {"name": "Event management", "item": [{"name": "create event", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"media\": {\r\n        \"image\": \"http://localhost:9090/mybucket/af4921a3-d24a-46a7-a970-dc9f3181e11e.png\",\r\n        \"banner\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\",\r\n        \"reel\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\"\r\n    },\r\n    \"startDatetime\": \"2024-09-15T15:30:00\",\r\n    \"endDatetime\":   \"2024-09-18T18:30:00\",\r\n    \"venueId\": \"1234\",\r\n    \"displayPrice\": \"20\",\r\n    \"eventAttributeLanguage\": {\r\n        \"title\": \"Test title\",\r\n        \"description\": \"Test description\",\r\n        \"shortDescription\": \"Test shortDescription\"\r\n    },\r\n    \"schedules\": [\r\n        {\r\n            \"name\": \"friday\",\r\n            \"start\": \"2024-07-23T15:30:00\",\r\n            \"end\": \"2024-07-23T15:30:00\"\r\n        },\r\n        {\r\n            \"name\": \"saturday\",\r\n            \"start\": \"2024-07-23T15:30:00\",\r\n            \"end\": \"2024-07-23T15:30:00\"\r\n        }\r\n    ],\r\n    \"bookingDeadline\": \"2024-07-23T15:30:00\",\r\n    \"confirmationRequired\": false,\r\n    \"lineup\": \"DJ,Drake,Kendrick\",\r\n    \"saveAsDraft\": false,\r\n    \"bookingTiers\": [\r\n        {\r\n            \"type\": \"TICKET\",\r\n            \"payOnlinePrice\": \"10\",\r\n            \"name\": \"booking tier name\",\r\n            \"description\": \"booking tier description\",\r\n            \"numberOfDrinks\": \"2\",\r\n            \"bottleCredit\": \"10\",\r\n            \"deposit\": \"100\",\r\n            \"minimumSpent\": \"300\",\r\n            \"schedules\": [\r\n                \"friday\",\r\n                \"saturday\"\r\n            ],\r\n            \"availability\": \"100\"\r\n            //needs tp be added\r\n        }\r\n    ],\r\n    \"minimumAge\": \"17+\",\r\n    \"musicStyleList\": [\r\n        \"Rock\",\r\n        \"EDM\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/events", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events"]}}, "response": [{"name": "New Request", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"media\": {\r\n        \"image\": \"http://localhost:9090/mybucket/af4921a3-d24a-46a7-a970-dc9f3181e11e.png\",\r\n        \"banner\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\",\r\n        \"reel\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\"\r\n    },\r\n    \"venueId\": \"1234\",\r\n    \"displayPrice\": \"20\",\r\n    \"eventAttributeLanguage\": {\r\n        \"title\": \"Test title\",\r\n        \"description\": \"Test description\",\r\n        \"shortDescription\": \"Test shortDescription\"\r\n    },\r\n    \"schedules\": [\r\n        {\r\n            \"name\": \"friday\",\r\n            \"start\": \"2024-07-23T15:30:00\",\r\n            \"end\": \"2024-07-23T15:30:00\"\r\n        },\r\n        {\r\n            \"name\": \"saturday\",\r\n            \"start\": \"2024-07-23T15:30:00\",\r\n            \"end\": \"2024-07-23T15:30:00\"\r\n        }\r\n    ],\r\n    \"bookingDeadline\": \"2024-07-23T15:30:00\",\r\n    \"confirmationRequired\": false,\r\n    \"lineup\": \"<PERSON>,<PERSON>,<PERSON>drick\",\r\n    \"saveAsDraft\": true,\r\n    \"bookingTiers\": [\r\n        {\r\n            \"type\": \"TICKET\",\r\n            \"payOnlinePrice\": \"10\",\r\n            \"name\": \"booking tier name\",\r\n            \"description\": \"booking tier description\",\r\n            \"numberOfDrinks\": \"2\",\r\n            \"bottleCredit\": \"10\",\r\n            \"deposit\": \"100\",\r\n            \"minimumSpent\": \"300\",\r\n            \"schedules\": [\r\n                \"friday\",\r\n                \"saturday\"\r\n            ],\r\n            \"availability\": \"100\"\r\n            //needs tp be added\r\n        }\r\n    ],\r\n    \"minimumAge\": \"17+\",\r\n    \"musicStyles\": [\r\n        \"Rock\",\r\n        \"EDM\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/events", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 09 Sep 2024 17:26:23 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"eventId\": 6\n}"}]}, {"name": "Get Event", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/events/7", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "7"]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/events/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "6"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 09 Sep 2024 17:32:18 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"id\": 6,\n    \"title\": \"Test title\",\n    \"displayPrice\": 20,\n    \"startTime\": null,\n    \"banner\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\",\n    \"minimumAge\": \"17+\",\n    \"musicStyleList\": [\n        \"Rock\",\n        \"EDM\"\n    ]\n}"}]}, {"name": "Partial update event", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"status\":\"PUBLISHED\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/events/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "6"]}}, "response": [{"name": "New Request", "originalRequest": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"status\":\"PUBLISHED\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/events/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "6"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 09 Sep 2024 17:36:26 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"eventId\": 6\n}"}]}, {"name": "Update Event", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"media\": {\r\n        \"image\": \"http://localhost:9090/mybucket/af4921a3-d24a-46a7-a970-dc9f3181e11e.png\",\r\n        \"banner\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\",\r\n        \"reel\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\"\r\n    },\r\n    \"startDatetime\": \"2024-09-09T15:30:00\",\r\n    \"endDatetime\": \"2024-09-09T16:30:00\",\r\n    \"venueId\": \"1234\",\r\n    \"displayPrice\": \"20\",\r\n    \"eventAttributeLanguage\": {\r\n        \"title\": \"Test title\",\r\n        \"description\": \"Test description\",\r\n        \"shortDescription\": \"Test shortDescription\"\r\n    },\r\n    \"schedules\": [\r\n        {\r\n            \"name\": \"friday\",\r\n            \"start\": \"2024-07-23T15:30:00\",\r\n            \"end\": \"2024-07-23T15:30:00\"\r\n        },\r\n        {\r\n            \"name\": \"saturday\",\r\n            \"start\": \"2024-07-23T15:30:00\",\r\n            \"end\": \"2024-07-23T15:30:00\"\r\n        }\r\n    ],\r\n    \"bookingDeadline\": \"2024-07-23T15:30:00\",\r\n    \"confirmationRequired\": false,\r\n    \"lineup\": \"DJ,Drake,Kendrick\",\r\n    \"status\": \"PUBLISHED\",\r\n    \"bookingTiers\": [\r\n        {\r\n            \"id\": 6,\r\n            \"type\": \"TICKET\",\r\n            \"payOnlinePrice\": \"10\",\r\n            \"name\": \"booking tier name\",\r\n            \"description\": \"booking tier description\",\r\n            \"numberOfDrinks\": \"2\",\r\n            \"bottleCredit\": \"10\",\r\n            \"deposit\": \"100\",\r\n            \"minimumSpent\": \"300\",\r\n            \"schedules\": [\r\n                \"friday\",\r\n                \"saturday\"\r\n            ],\r\n            \"availability\": \"100\"\r\n            //needs tp be added\r\n        }\r\n    ],\r\n    \"minimumAge\": \"17+\",\r\n    \"musicStyles\": [\r\n        \"Rock\",\r\n        \"EDM\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/events/7", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "7"]}}, "response": [{"name": "Update Event", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"media\": {\r\n        \"image\": \"http://localhost:9090/mybucket/af4921a3-d24a-46a7-a970-dc9f3181e11e.png\",\r\n        \"banner\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\",\r\n        \"reel\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\"\r\n    },\r\n    \"venueId\": \"1234\",\r\n    \"displayPrice\": \"20\",\r\n    \"eventAttributeLanguage\": {\r\n        \"title\": \"Test title\",\r\n        \"description\": \"Test description\",\r\n        \"shortDescription\": \"Test shortDescription\"\r\n    },\r\n    \"schedules\": [\r\n        {\r\n            \"name\": \"friday\",\r\n            \"start\": \"2024-07-23T15:30:00\",\r\n            \"end\": \"2024-07-23T15:30:00\"\r\n        },\r\n        {\r\n            \"name\": \"saturday\",\r\n            \"start\": \"2024-07-23T15:30:00\",\r\n            \"end\": \"2024-07-23T15:30:00\"\r\n        }\r\n    ],\r\n    \"bookingDeadline\": \"2024-07-23T15:30:00\",\r\n    \"confirmationRequired\": false,\r\n    \"lineup\": \"<PERSON>,<PERSON>,<PERSON>drick\",\r\n    \"status\": \"DRAFT\",\r\n    \"bookingTiers\": [\r\n        {\r\n            \"id\":6,\r\n            \"type\": \"TICKET\",\r\n            \"payOnlinePrice\": \"10\",\r\n            \"name\": \"booking tier name\",\r\n            \"description\": \"booking tier description\",\r\n            \"numberOfDrinks\": \"2\",\r\n            \"bottleCredit\": \"10\",\r\n            \"deposit\": \"100\",\r\n            \"minimumSpent\": \"300\",\r\n            \"schedules\": [\r\n                \"friday\",\r\n                \"saturday\"\r\n            ],\r\n            \"availability\": \"100\"\r\n            //needs tp be added\r\n        }\r\n    ],\r\n    \"minimumAge\": \"17+\",\r\n    \"musicStyles\": [\r\n        \"Rock\",\r\n        \"EDM\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8080/v1/events/6", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "6"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 09 Sep 2024 17:42:08 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"eventId\": 6\n}"}]}, {"name": "Get events for venue", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/events/venue/1234", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "venue", "1234"]}}, "response": [{"name": "New Request", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/events/venue/1234", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "venue", "1234"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 09 Sep 2024 17:57:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "[\n    {\n        \"id\": 7,\n        \"title\": \"Test title\",\n        \"displayPrice\": 20,\n        \"startTime\": null,\n        \"banner\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\",\n        \"minimumAge\": \"17+\",\n        \"bookingTiers\": [\n            {\n                \"type\": \"TICKET\",\n                \"payOnlinePrice\": 10,\n                \"tierName\": null,\n                \"description\": null,\n                \"numberOfDrinks\": null,\n                \"bottleCredit\": null,\n                \"minimumSpent\": null,\n                \"includedConsumptionAmount\": null,\n                \"includedConsumptionDescription\": null,\n                \"bookingTierSchedules\": [],\n                \"availability\": null,\n                \"eventId\": 7\n            }\n        ],\n        \"musicStyleList\": [\n            \"Rock\",\n            \"EDM\"\n        ]\n    }\n]"}, {"name": "Get events for venue", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/events/venue/1234?sort=startDatetime,desc", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "venue", "1234"], "query": [{"key": "sort", "value": "startDatetime,desc"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Mon, 09 Sep 2024 18:29:13 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"totalElements\": 2,\n    \"totalPages\": 1,\n    \"size\": 20,\n    \"content\": [\n        {\n            \"id\": 9,\n            \"title\": \"Test title\",\n            \"displayPrice\": 20,\n            \"startDatetime\": \"2024-09-15T15:30:00.000+00:00\",\n            \"endDatetime\": \"2024-09-18T18:30:00.000+00:00\",\n            \"banner\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\",\n            \"minimumAge\": \"17+\",\n            \"bookingTiers\": [\n                {\n                    \"type\": \"TICKET\",\n                    \"payOnlinePrice\": 10,\n                    \"tierName\": null,\n                    \"description\": null,\n                    \"numberOfDrinks\": null,\n                    \"bottleCredit\": null,\n                    \"minimumSpent\": null,\n                    \"includedConsumptionAmount\": null,\n                    \"includedConsumptionDescription\": null,\n                    \"bookingTierSchedules\": [],\n                    \"availability\": null,\n                    \"eventId\": 9\n                }\n            ],\n            \"musicStyleList\": [\n                \"Rock\",\n                \"EDM\"\n            ]\n        },\n        {\n            \"id\": 7,\n            \"title\": \"Test title\",\n            \"displayPrice\": 20,\n            \"startDatetime\": \"2024-09-09T15:30:00.000+00:00\",\n            \"endDatetime\": \"2024-09-15T20:30:00.000+00:00\",\n            \"banner\": \"http://localhost:9090/mybucket/9021ef0c-39f1-4028-a069-a1bd182cb53b.png\",\n            \"minimumAge\": \"17+\",\n            \"bookingTiers\": [\n                {\n                    \"type\": \"TICKET\",\n                    \"payOnlinePrice\": 10,\n                    \"tierName\": null,\n                    \"description\": null,\n                    \"numberOfDrinks\": null,\n                    \"bottleCredit\": null,\n                    \"minimumSpent\": null,\n                    \"includedConsumptionAmount\": null,\n                    \"includedConsumptionDescription\": null,\n                    \"bookingTierSchedules\": [],\n                    \"availability\": null,\n                    \"eventId\": 7\n                }\n            ],\n            \"musicStyleList\": [\n                \"Rock\",\n                \"EDM\"\n            ]\n        }\n    ],\n    \"number\": 0,\n    \"sort\": {\n        \"empty\": false,\n        \"sorted\": true,\n        \"unsorted\": false\n    },\n    \"first\": true,\n    \"last\": true,\n    \"numberOfElements\": 2,\n    \"pageable\": {\n        \"pageNumber\": 0,\n        \"pageSize\": 20,\n        \"sort\": {\n            \"empty\": false,\n            \"sorted\": true,\n            \"unsorted\": false\n        },\n        \"offset\": 0,\n        \"paged\": true,\n        \"unpaged\": false\n    },\n    \"empty\": false\n}"}]}, {"name": "Delete Event", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://localhost:8080/v1/events/8", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "8"]}}, "response": []}, {"name": "Get events for Venue ( fitlered)", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8080/v1/events/venue/1234?status=ONGOING", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["v1", "events", "venue", "1234"], "query": [{"key": "status", "value": "ONGOING"}]}}, "response": []}]}]}